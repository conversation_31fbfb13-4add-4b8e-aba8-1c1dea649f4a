name: stable-linux

on:
  workflow_dispatch:
    inputs:
      force_version:
        type: boolean
        description: Force update version
      generate_assets:
        type: boolean
        description: Generate assets
      checkout_pr:
        type: string
        description: Checkout PR
  repository_dispatch:
    types: [stable]
  push:
    branches: [ master ]
    paths-ignore:
    - '**/*.md'
    - 'upstream/*.json'
  pull_request:
    branches: [ master ]
    paths-ignore:
    - '**/*.md'

env:
  ACTIONS_ALLOW_USE_UNSECURE_NODE_VERSION: true
  APP_NAME: VSCodium
  ASSETS_REPOSITORY: ${{ github.repository }}
  BINARY_NAME: codium
  DISABLE_UPDATE: 'yes'
  GH_REPO_PATH: ${{ github.repository }}
  ORG_NAME: ${{ github.repository_owner }}
  OS_NAME: linux
  VERSIONS_REPOSITORY: ${{ github.repository_owner }}/versions
  VSCODE_QUALITY: stable

jobs:
  check:
    runs-on: ubuntu-latest
    outputs:
      MS_COMMIT: ${{ env.MS_COMMIT }}
      MS_TAG: ${{ env.MS_TAG }}
      RELEASE_VERSION: ${{ env.RELEASE_VERSION }}
      SHOULD_BUILD: ${{ env.SHOULD_BUILD }}
      SHOULD_DEPLOY: ${{ env.SHOULD_DEPLOY }}

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ env.GITHUB_BRANCH }}

      - name: Switch to relevant branch
        env:
          PULL_REQUEST_ID: ${{ github.event.inputs.checkout_pr }}
        run: ./get_pr.sh

      - name: Clone VSCode repo
        run: ./get_repo.sh

      - name: Check PR or cron
        env:
          GENERATE_ASSETS: ${{ github.event.inputs.generate_assets }}
        run: ./check_cron_or_pr.sh

      - name: Check existing VSCodium tags/releases
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CHECK_ALL: 'yes'
        run: ./check_tags.sh

  compile:
    needs:
      - check
    runs-on: ubuntu-22.04
    env:
      MS_COMMIT: ${{ needs.check.outputs.MS_COMMIT }}
      MS_TAG: ${{ needs.check.outputs.MS_TAG }}
      RELEASE_VERSION: ${{ needs.check.outputs.RELEASE_VERSION }}
      SHOULD_BUILD: ${{ (needs.check.outputs.SHOULD_BUILD == 'yes' || github.event.inputs.generate_assets == 'true') && 'yes' || 'no' }}
      VSCODE_ARCH: 'x64'
    outputs:
      BUILD_SOURCEVERSION: ${{ env.BUILD_SOURCEVERSION }}

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ env.GITHUB_BRANCH }}
        if: env.SHOULD_BUILD == 'yes'

      - name: Switch to relevant branch
        env:
          PULL_REQUEST_ID: ${{ github.event.inputs.checkout_pr }}
        run: ./get_pr.sh
        if: env.SHOULD_BUILD == 'yes'

      - name: Setup GCC
        uses: egor-tensin/setup-gcc@v1
        with:
          version: 10
          platform: x64
        if: env.SHOULD_BUILD == 'yes'

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: '22.14.0'
        if: env.SHOULD_BUILD == 'yes'

      - name: Setup Python 3
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
        if: env.SHOULD_BUILD == 'yes'

      - name: Install libkrb5-dev
        run: sudo apt-get update -y && sudo apt-get install -y libkrb5-dev
        if: env.SHOULD_BUILD == 'yes'

      - name: Clone VSCode repo
        run: ./get_repo.sh
        if: env.SHOULD_BUILD == 'yes'

      - name: Build
        env:
          SHOULD_BUILD_REH: 'no'
          SHOULD_BUILD_REH_WEB: 'no'
        run: ./build.sh
        if: env.SHOULD_BUILD == 'yes'

      - name: Compress vscode artifact
        run: |
          find vscode -type f -not -path "*/node_modules/*" -not -path "vscode/.build/node/*" -not -path "vscode/.git/*" > vscode.txt
          echo "vscode/.build/extensions/node_modules" >> vscode.txt
          echo "vscode/.git" >> vscode.txt
          tar -czf vscode.tar.gz -T vscode.txt
        if: env.SHOULD_BUILD == 'yes'

      - name: Upload vscode artifact
        uses: actions/upload-artifact@v4
        with:
          name: vscode
          path: ./vscode.tar.gz
          retention-days: ${{ needs.check.outputs.SHOULD_DEPLOY == 'yes' && 30 || 1 }}
        if: env.SHOULD_BUILD == 'yes'

  build:
    needs:
      - check
      - compile
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        include:
        - slug: X64
          vscode_arch: x64
          npm_arch: x64
          image: vscodium/vscodium-linux-build-agent:focal-x64
        - slug: ARM64
          vscode_arch: arm64
          npm_arch: arm64
          image: vscodium/vscodium-linux-build-agent:focal-arm64
        - slug: ARM32
          vscode_arch: armhf
          npm_arch: arm
          image: vscodium/vscodium-linux-build-agent:focal-armhf
        - slug: RISCV64
          vscode_arch: riscv64
          npm_arch: riscv64
          image: vscodium/vscodium-linux-build-agent:focal-riscv64
        - slug: LOONG64
          vscode_arch: loong64
          npm_arch: loong64
          image: vscodium/vscodium-linux-build-agent:beige-loong64
        - slug: PPC64
          vscode_arch: ppc64le
          npm_arch: ppc64
          image: vscodium/vscodium-linux-build-agent:focal-ppc64le
    container:
      image: ${{ matrix.image }}
    env:
      BUILD_SOURCEVERSION: ${{ needs.compile.outputs.BUILD_SOURCEVERSION }}
      DISABLED: ${{ vars[format('DISABLE_STABLE_LINUX_APP_{0}', matrix.slug)] }}
      MS_COMMIT: ${{ needs.check.outputs.MS_COMMIT }}
      MS_TAG: ${{ needs.check.outputs.MS_TAG }}
      RELEASE_VERSION: ${{ needs.check.outputs.RELEASE_VERSION }}
      SHOULD_BUILD: ${{ (needs.check.outputs.SHOULD_BUILD == 'yes' || github.event.inputs.generate_assets == 'true') && 'yes' || 'no' }}
      SHOULD_DEPLOY: ${{ needs.check.outputs.SHOULD_DEPLOY }}
      VSCODE_ARCH: ${{ matrix.vscode_arch }}
    outputs:
      RELEASE_VERSION: ${{ env.RELEASE_VERSION }}
      SHOULD_BUILD: ${{ env.SHOULD_BUILD }}
      SHOULD_DEPLOY: ${{ env.SHOULD_DEPLOY }}

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ env.GITHUB_BRANCH }}
        if: env.DISABLED != 'yes' && env.SHOULD_BUILD == 'yes'

      - name: Switch to relevant branch
        env:
          PULL_REQUEST_ID: ${{ github.event.inputs.checkout_pr }}
        run: ./get_pr.sh
        if: env.DISABLED != 'yes'

      - name: Install GH
        run: ./install_gh.sh
        if: env.DISABLED != 'yes' && env.SHOULD_BUILD == 'yes' && env.SHOULD_DEPLOY == 'yes'

      - name: Check existing VSCodium tags/releases
        env:
          CHECK_REH: 'no'
          DISABLE_APPIMAGE: ${{ vars.DISABLE_STABLE_APPIMAGE }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: ./check_tags.sh
        if: env.DISABLED != 'yes' && env.SHOULD_BUILD == 'yes'

      - name: Install dependencies
        run: ./build/linux/deps.sh
        if: env.DISABLED != 'yes' && env.SHOULD_BUILD == 'yes'

      - uses: actions-rust-lang/setup-rust-toolchain@v1
        if: env.DISABLED != 'yes' && env.SHOULD_BUILD == 'yes'

      - name: Download vscode artifact
        uses: actions/download-artifact@v4
        with:
          name: vscode
        if: env.DISABLED != 'yes' && env.SHOULD_BUILD == 'yes'

      - name: Build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          npm_config_arch: ${{ matrix.npm_arch }}
        run: ./build/linux/package_bin.sh
        if: env.DISABLED != 'yes' && env.SHOULD_BUILD == 'yes'

      - name: Prepare assets
        env:
          SHOULD_BUILD_REH: 'no'
          SHOULD_BUILD_REH_WEB: 'no'
        run: ./prepare_assets.sh
        if: env.DISABLED != 'yes' && env.SHOULD_BUILD == 'yes' && (env.SHOULD_DEPLOY == 'yes' || github.event.inputs.generate_assets == 'true')

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.STRONGER_GITHUB_TOKEN }}
          GITHUB_USERNAME: ${{ github.repository_owner }}
        run: ./release.sh
        if: env.DISABLED != 'yes' && env.SHOULD_BUILD == 'yes' && env.SHOULD_DEPLOY == 'yes'

      - name: Update versions repo
        env:
          FORCE_UPDATE: ${{ github.event.inputs.force_version }}
          GITHUB_TOKEN: ${{ secrets.STRONGER_GITHUB_TOKEN }}
          GITHUB_USERNAME: ${{ github.repository_owner }}
        run: ./update_version.sh
        if: env.DISABLED != 'yes' && env.SHOULD_BUILD == 'yes' && env.SHOULD_DEPLOY == 'yes'

      - name: Upload assets
        uses: actions/upload-artifact@v4
        with:
          name: bin-${{ matrix.vscode_arch }}
          path: assets/
          retention-days: 3
        if: env.DISABLED != 'yes' && env.SHOULD_BUILD == 'yes' && env.SHOULD_DEPLOY == 'no' && github.event.inputs.generate_assets == 'true'

  reh_linux:
    needs:
      - check
      - compile
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        include:
        - slug: X64
          vscode_arch: x64
          npm_arch: x64
        - slug: ARM64
          vscode_arch: arm64
          npm_arch: arm64
        - slug: ARM32
          vscode_arch: armhf
          npm_arch: arm
        - slug: PPC64
          vscode_arch: ppc64le
          npm_arch: ppc64
        - slug: RISCV64
          vscode_arch: riscv64
          npm_arch: riscv64
        - slug: LOONG64
          vscode_arch: loong64
          npm_arch: loong64
        - slug: S390X
          vscode_arch: s390x
          npm_arch: s390x
    env:
      BUILD_SOURCEVERSION: ${{ needs.compile.outputs.BUILD_SOURCEVERSION }}
      DISABLED: ${{ vars[format('DISABLE_STABLE_LINUX_REH_{0}', matrix.slug)] }}
      MS_COMMIT: ${{ needs.check.outputs.MS_COMMIT }}
      MS_TAG: ${{ needs.check.outputs.MS_TAG }}
      RELEASE_VERSION: ${{ needs.check.outputs.RELEASE_VERSION }}
      SHOULD_BUILD: ${{ needs.check.outputs.SHOULD_BUILD }}
      SHOULD_DEPLOY: ${{ needs.check.outputs.SHOULD_DEPLOY }}
      VSCODE_ARCH: ${{ matrix.vscode_arch }}
    if: needs.check.outputs.SHOULD_BUILD == 'yes' || github.event.inputs.generate_assets == 'true'

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ env.GITHUB_BRANCH }}
        if: env.DISABLED != 'yes'

      - name: Switch to relevant branch
        env:
          PULL_REQUEST_ID: ${{ github.event.inputs.checkout_pr }}
        run: ./get_pr.sh
        if: env.DISABLED != 'yes'

      - name: Setup GCC
        uses: egor-tensin/setup-gcc@v1
        with:
          version: 10
          platform: x64
        if: env.DISABLED != 'yes'

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: '22.14.0'
        if: env.DISABLED != 'yes'

      - name: Setup Python 3
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
        if: env.DISABLED != 'yes'

      - name: Install libkrb5-dev
        run: sudo apt-get update -y && sudo apt-get install -y libkrb5-dev
        if: env.DISABLED != 'yes'

      - name: Install GH
        run: ./install_gh.sh
        if: env.DISABLED != 'yes' && env.SHOULD_DEPLOY == 'yes'

      - name: Check existing VSCodium tags/releases
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CHECK_ONLY_REH: 'yes'
        run: ./check_tags.sh
        if: env.DISABLED != 'yes'

      - name: Download vscode artifact
        uses: actions/download-artifact@v4
        with:
          name: vscode
        if: env.DISABLED != 'yes' && (env.SHOULD_BUILD_REH != 'no' || env.SHOULD_BUILD_REH_WEB != 'no' || github.event.inputs.generate_assets == 'true')

      - name: Build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          npm_config_arch: ${{ matrix.npm_arch }}
        run: ./build/linux/package_reh.sh
        if: env.DISABLED != 'yes' && (env.SHOULD_BUILD_REH != 'no' || env.SHOULD_BUILD_REH_WEB != 'no' || github.event.inputs.generate_assets == 'true')

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.STRONGER_GITHUB_TOKEN }}
          GITHUB_USERNAME: ${{ github.repository_owner }}
        run: ./release.sh
        if: env.DISABLED != 'yes' && env.SHOULD_DEPLOY == 'yes' && (env.SHOULD_BUILD_REH != 'no' || env.SHOULD_BUILD_REH_WEB != 'no')

      - name: Upload assets
        uses: actions/upload-artifact@v4
        with:
          name: reh-linux-${{ matrix.vscode_arch }}
          path: assets/
          retention-days: 3
        if: env.DISABLED != 'yes' && env.SHOULD_DEPLOY == 'no' && github.event.inputs.generate_assets == 'true'

  reh_alpine:
    needs:
      - check
      - compile
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        include:
        - slug: X64
          vscode_arch: x64
          npm_arch: x64
        - slug: ARM64
          vscode_arch: arm64
          npm_arch: arm64
    env:
      BUILD_SOURCEVERSION: ${{ needs.compile.outputs.BUILD_SOURCEVERSION }}
      DISABLED: ${{ vars[format('DISABLE_STABLE_ALPINE_REH_{0}', matrix.slug)] }}
      MS_COMMIT: ${{ needs.check.outputs.MS_COMMIT }}
      MS_TAG: ${{ needs.check.outputs.MS_TAG }}
      OS_NAME: alpine
      RELEASE_VERSION: ${{ needs.check.outputs.RELEASE_VERSION }}
      SHOULD_BUILD: ${{ needs.check.outputs.SHOULD_BUILD }}
      SHOULD_DEPLOY: ${{ needs.check.outputs.SHOULD_DEPLOY }}
      VSCODE_ARCH: ${{ matrix.vscode_arch }}
    if: needs.check.outputs.SHOULD_BUILD == 'yes' || github.event.inputs.generate_assets == 'true'

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ env.GITHUB_BRANCH }}

      - name: Switch to relevant branch
        env:
          PULL_REQUEST_ID: ${{ github.event.inputs.checkout_pr }}
        run: ./get_pr.sh

      - name: Setup GCC
        uses: egor-tensin/setup-gcc@v1
        with:
          version: 10
          platform: x64

      - name: Install GH
        run: ./install_gh.sh
        if: env.SHOULD_DEPLOY == 'yes'

      - name: Check existing VSCodium tags/releases
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CHECK_ONLY_REH: 'yes'
        run: ./check_tags.sh

      - name: Install libkrb5-dev
        run: sudo apt-get update -y && sudo apt-get install -y libkrb5-dev
        if: env.SHOULD_BUILD == 'yes'

      - name: Download vscode artifact
        uses: actions/download-artifact@v4
        with:
          name: vscode
        if: env.DISABLED != 'yes' && (env.SHOULD_BUILD_REH != 'no' || env.SHOULD_BUILD_REH_WEB != 'no' || github.event.inputs.generate_assets == 'true')

      - name: Build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          npm_config_arch: ${{ matrix.npm_arch }}
        run: ./build/alpine/package_reh.sh
        if: env.DISABLED != 'yes' && (env.SHOULD_BUILD_REH != 'no' || env.SHOULD_BUILD_REH_WEB != 'no' || github.event.inputs.generate_assets == 'true')

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.STRONGER_GITHUB_TOKEN }}
          GITHUB_USERNAME: ${{ github.repository_owner }}
        run: ./release.sh
        if: env.DISABLED != 'yes' && env.SHOULD_DEPLOY == 'yes' && (env.SHOULD_BUILD_REH != 'no' || env.SHOULD_BUILD_REH_WEB != 'no')

      - name: Upload assets
        uses: actions/upload-artifact@v4
        with:
          name: reh-alpine-${{ matrix.vscode_arch }}
          path: assets/
          retention-days: 3
        if: env.DISABLED != 'yes' && env.SHOULD_DEPLOY == 'no' && github.event.inputs.generate_assets == 'true'

  aur:
    needs:
      - check
      - build
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        include:
        - package_name: vscodium
          package_type: stable
        # - package_name: vscodium-git
        #   package_type: rolling
    if: needs.check.outputs.SHOULD_DEPLOY == 'yes'

    steps:
      - name: Publish ${{ matrix.package_name }}
        uses: zokugun/github-actions-aur-releaser@v1
        with:
          package_name: ${{ matrix.package_name }}
          package_type: ${{ matrix.package_type }}
          aur_private_key: ${{ secrets.AUR_PRIVATE_KEY }}
          aur_username: ${{ secrets.AUR_USERNAME }}
          aur_email: ${{ secrets.AUR_EMAIL }}

  snap:
    needs:
      - check
      - build
    runs-on: ubuntu-latest
    env:
      RELEASE_VERSION: ${{ needs.check.outputs.RELEASE_VERSION }}
      SNAP_NAME: codium
    strategy:
      fail-fast: false
      matrix:
        platform:
        - amd64
        - arm64
    if: needs.check.outputs.SHOULD_DEPLOY == 'yes' && needs.check.outputs.SHOULD_BUILD_SNAP != 'no' && vars.DISABLE_STABLE_SNAP != 'yes'

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ env.GITHUB_BRANCH }}

      - name: Switch to relevant branch
        env:
          PULL_REQUEST_ID: ${{ github.event.inputs.checkout_pr }}
        run: ./get_pr.sh

      - name: Check version
        env:
          ARCHITECTURE: ${{ matrix.platform }}
          GENERATE_ASSETS: ${{ github.event.inputs.generate_assets }}
          SNAPCRAFT_STORE_CHANNEL: ${{ vars.SNAP_STORE_CHANNEL }}
          SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.SNAP_STORE_LOGIN }}
        run: ./stores/snapcraft/check_version.sh

      - uses: docker/setup-qemu-action@v3
        if: env.SHOULD_BUILD == 'yes'

      - uses: diddlesnaps/snapcraft-multiarch-action@v1
        with:
          path: stores/snapcraft/stable
          architecture: ${{ matrix.platform }}
        id: build
        if: env.SHOULD_BUILD == 'yes'

      - uses: diddlesnaps/snapcraft-review-action@v1
        with:
          snap: ${{ steps.build.outputs.snap }}
          isClassic: 'true'
        if: env.SHOULD_DEPLOY_TO_RELEASE == 'yes' || env.SHOULD_DEPLOY_TO_STORE == 'yes'

      - uses: svenstaro/upload-release-action@v2
        with:
          repo_name: ${{ env.ASSETS_REPOSITORY }}
          repo_token: ${{ secrets.STRONGER_GITHUB_TOKEN }}
          file: ${{ steps.build.outputs.snap }}
          tag: ${{ env.RELEASE_VERSION }}
        if: env.SHOULD_DEPLOY_TO_RELEASE == 'yes'

      - uses: snapcore/action-publish@master
        env:
          SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.SNAP_STORE_LOGIN }}
        with:
          snap: ${{ steps.build.outputs.snap }}
          release: ${{ vars.SNAP_STORE_CHANNEL }}
        if: env.SHOULD_DEPLOY_TO_STORE == 'yes'

  deploy-repo-rudy:
    needs:
      - check
      - build
    runs-on: ubuntu-latest
    if: needs.check.outputs.SHOULD_DEPLOY == 'yes' && github.event.inputs.generate_assets != 'true'

    steps:
      - name: Trigger repository rebuild
        env:
          GL_PAGES_TOKEN: ${{ secrets.GL_PAGES_TOKEN }}
        run: curl -X POST --fail -F token="${GL_PAGES_TOKEN}" -F ref=master https://gitlab.com/api/v4/projects/8762263/trigger/pipeline

  deploy-repo-dev:
    needs:
      - check
      - build
    runs-on: ubuntu-latest
    if: needs.check.outputs.SHOULD_DEPLOY == 'yes' && github.event.inputs.generate_assets != 'true'

    steps:
      - name: Trigger repository rebuild
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ secrets.STRONGER_GITHUB_TOKEN }}
          repository: VSCodium/repositories-linux
          event-type: deploy
