name: stable-macos

on:
  workflow_dispatch:
    inputs:
      force_version:
        type: boolean
        description: Force update version
      generate_assets:
        type: boolean
        description: Generate assets
      checkout_pr:
        type: string
        description: Checkout PR
  repository_dispatch:
    types: [stable]
  push:
    branches: [ master ]
    paths-ignore:
    - '**/*.md'
    - 'upstream/*.json'
  pull_request:
    branches: [ master ]
    paths-ignore:
    - '**/*.md'

env:
  APP_NAME: VSCodium
  ASSETS_REPOSITORY: ${{ github.repository }}
  BINARY_NAME: codium
  GH_REPO_PATH: ${{ github.repository }}
  ORG_NAME: ${{ github.repository_owner }}
  OS_NAME: osx
  VERSIONS_REPOSITORY: ${{ github.repository_owner }}/versions
  VSCODE_QUALITY: stable

jobs:
  build:
    runs-on: ${{ matrix.runner }}
    env:
      VSCODE_ARCH: ${{ matrix.vscode_arch }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - runner: macos-13
            vscode_arch: x64
          - runner: [self-hosted, macOS, ARM64]
            vscode_arch: arm64

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ env.GITHUB_BRANCH }}

      - name: Switch to relevant branch
        env:
          PULL_REQUEST_ID: ${{ github.event.inputs.checkout_pr }}
        run: . get_pr.sh

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: '22.14.0'

      - name: Setup Python 3
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'
        if: env.VSCODE_ARCH == 'x64'

      - name: Clone VSCode repo
        run: . get_repo.sh

      - name: Check PR or cron
        env:
          GENERATE_ASSETS: ${{ github.event.inputs.generate_assets }}
        run: . check_cron_or_pr.sh

      - name: Check existing VSCodium tags/releases
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: . check_tags.sh
        if: env.SHOULD_DEPLOY == 'yes'

      - name: Build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: ./build.sh
        if: env.SHOULD_BUILD == 'yes'

      - name: Prepare assets
        env:
          CERTIFICATE_OSX_APP_PASSWORD: ${{ secrets.CERTIFICATE_OSX_NEW_APP_PASSWORD }}
          CERTIFICATE_OSX_ID: ${{ secrets.CERTIFICATE_OSX_NEW_ID }}
          CERTIFICATE_OSX_P12_DATA: ${{ secrets.CERTIFICATE_OSX_NEW_P12_DATA }}
          CERTIFICATE_OSX_P12_PASSWORD: ${{ secrets.CERTIFICATE_OSX_NEW_P12_PASSWORD }}
          CERTIFICATE_OSX_TEAM_ID: ${{ secrets.CERTIFICATE_OSX_NEW_TEAM_ID }}
        run: ./prepare_assets.sh
        if: env.SHOULD_BUILD == 'yes' && (env.SHOULD_DEPLOY == 'yes' || github.event.inputs.generate_assets == 'true')

      - name: Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: ./release.sh
        if: env.SHOULD_BUILD == 'yes' && env.SHOULD_DEPLOY == 'yes'

      - name: Update versions repo
        env:
          FORCE_UPDATE: ${{ github.event.inputs.force_version }}
          GITHUB_TOKEN: ${{ secrets.STRONGER_GITHUB_TOKEN }}
          GITHUB_USERNAME: ${{ github.repository_owner }}
        run: ./update_version.sh
        if: env.SHOULD_DEPLOY == 'yes'

      - name: Upload assets
        uses: actions/upload-artifact@v4
        with:
          name: bin-${{ matrix.vscode_arch }}
          path: assets/
          retention-days: 3
        if: env.SHOULD_BUILD == 'yes' && env.SHOULD_DEPLOY == 'no' && github.event.inputs.generate_assets == 'true'

      - name: Clean up keychain
        if: always()
        run: |
          KEYCHAIN=$RUNNER_TEMP/build.keychain

          if [ -f "$KEYCHAIN" ];
          then
            security delete-keychain $KEYCHAIN
          fi
