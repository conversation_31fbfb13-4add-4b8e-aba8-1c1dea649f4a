name: Stale Issues

on:
  schedule:
    - cron: '0 1 * * *'

permissions:
  issues: write

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          days-before-stale: 180
          days-before-close: 30
          stale-issue-label: stale
          operations-per-run: 1024
          stale-issue-message: >
            This issue has been automatically marked as stale. **If this issue is still affecting you, please leave any comment**, and we'll keep it open. If you have any new additional information, please include it with your comment!
          close-issue-message: >
            This issue has been closed due to inactivity, and will not be monitored. If this is a bug and you can reproduce this issue, please open a new issue.
          exempt-issue-labels: discussion,never-stale
          only-pr-labels: needs-information
