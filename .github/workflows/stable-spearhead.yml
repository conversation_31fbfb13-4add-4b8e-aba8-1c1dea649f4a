name: stable-spearhead

on:
  workflow_dispatch:
    inputs:
      new_release:
        type: boolean
        description: Force new Release
      force_dispatch:
        type: boolean
        description: Force dispatch
      dont_update:
        type: boolean
        description: Don't update VSCode
  schedule:
    - cron: '0 18 * * *'

jobs:
  build:
    runs-on: macos-14
    env:
      APP_NAME: VSCodium
      ASSETS_REPOSITORY: ${{ github.repository }}
      BINARY_NAME: codium
      GH_REPO_PATH: ${{ github.repository }}
      ORG_NAME: ${{ github.repository_owner }}
      OS_NAME: osx
      VERSIONS_REPOSITORY: ${{ github.repository_owner }}/versions
      VSCODE_ARCH: arm64
      VSCODE_LATEST: ${{ github.event.inputs.dont_update == 'true' && 'no' || 'yes' }}
      VSCODE_QUALITY: stable

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: '22.14.0'

      - name: <PERSON>lone VSCode repo
        run: . get_repo.sh

      - name: Check existing VSCodium tags/releases
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NEW_RELEASE: ${{ github.event.inputs.new_release }}
          IS_SPEARHEAD: 'yes'
        run: . check_tags.sh

      - name: Build
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: ./build.sh
        if: env.SHOULD_BUILD == 'yes'

      - name: Import GPG key
        uses: crazy-max/ghaction-import-gpg@v6
        with:
          gpg_private_key: ${{ secrets.GPG_PRIVATE_KEY }}
          passphrase: ${{ secrets.GPG_PASSPHRASE }}
          git_user_signingkey: true
          git_commit_gpgsign: true
        if: env.SHOULD_BUILD == 'yes' && github.event.inputs.dont_update != 'true'

      - name: Update upstream version
        run: ./update_upstream.sh
        if: env.SHOULD_BUILD == 'yes' && github.event.inputs.dont_update != 'true'

      - name: Prepare source
        run: ./prepare_src.sh
        if: env.SHOULD_BUILD == 'yes'

      - name: Release source
        env:
          GITHUB_TOKEN: ${{ secrets.STRONGER_GITHUB_TOKEN }}
          GITHUB_USERNAME: ${{ github.repository_owner }}
        run: ./release.sh
        if: env.SHOULD_BUILD == 'yes'

      - name: Dispatch builds
        uses: peter-evans/repository-dispatch@v3
        with:
          event-type: stable
        if: env.SHOULD_BUILD == 'yes' || github.event.inputs.force_dispatch == 'true'
