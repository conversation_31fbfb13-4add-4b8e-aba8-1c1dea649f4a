<!-- order: 5 -->

# Getting Started with VSCodium

This guide will help you get started with VSCodium.

## Table of Contents

- [Installation](#installation)
- [First Steps](#first-steps)
- [Basic Usage](#basic-usage)
- [Keyboard Shortcuts](#keyboard-shortcuts)
- [Next Steps](#next-steps)

## <a id="installation"></a>Installation

VSCodium can be installed on Windows, macOS, and Linux. Visit the [download page](https://vscodium.com/#install) for installation instructions.

## <a id="first-steps"></a>First Steps

After installing VSCodium, here are some first steps to get started:

1. **Open a folder**: Use File > Open Folder to open your project
2. **Install extensions**: Click on the Extensions icon in the sidebar to browse and install extensions
3. **Configure settings**: Use File > Preferences > Settings to customize your editor

## <a id="basic-usage"></a>Basic Usage

VSCodium works just like Visual Studio Code, with a few differences:

- It uses Open VSX for extensions by default instead of the Visual Studio Marketplace
- It doesn't include Microsoft telemetry or branding
- Some proprietary features may not be available

## <a id="keyboard-shortcuts"></a>Keyboard Shortcuts

Here are some essential keyboard shortcuts to get you started:

- `Ctrl+P` (Windows/Linux) or `Cmd+P` (macOS): Quick Open, Go to File
- `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (macOS): Show Command Palette
- `Ctrl+,` (Windows/Linux) or `Cmd+,` (macOS): User Settings
- `Ctrl+K Ctrl+S` (Windows/Linux) or `Cmd+K Cmd+S` (macOS): Keyboard Shortcuts

## <a id="next-steps"></a>Next Steps

Once you're comfortable with the basics, you might want to:

- Explore the [documentation](https://github.com/VSCodium/vscodium/blob/master/docs/index.md) for more details
- Join the [community](https://github.com/VSCodium/vscodium/discussions) to ask questions and share tips
- Contribute to the [project](https://github.com/VSCodium/vscodium/blob/master/CONTRIBUTING.md) if you're interested
