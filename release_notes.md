update vscode to [@@MS_TAG@@](@@MS_URL@@)

@@RELEASE_NOTES@@

## x86 64bits

<table>
  <tr>
    <td rowspan="8">Windows</td>
    <td>User Installer</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@UserSetup-x64-@@VERSION@@@@QUALITY@@.exe">@@APP_NAME@@UserSetup-x64-@@VERSION@@@@QUALITY@@.exe</a></td>
  </tr>
  <tr>
    <td>System Installer</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@Setup-x64-@@VERSION@@@@QUALITY@@.exe">@@APP_NAME@@Setup-x64-@@VERSION@@@@QUALITY@@.exe</a></td>
  </tr>
  <tr>
    <td>.zip</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-win32-x64-@@VERSION@@@@QUALITY@@.zip">@@APP_NAME@@-win32-x64-@@VERSION@@@@QUALITY@@.zip</a></td>
  </tr>
  <tr>
    <td>.msi - updates enabled</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-x64-@@VERSION@@@@QUALITY@@.msi">@@APP_NAME@@-x64-@@VERSION@@@@QUALITY@@.msi</a></td>
  </tr>
  <tr>
    <td>.msi - updates disabled</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-x64-updates-disabled-@@VERSION@@@@QUALITY@@.msi">@@APP_NAME@@-x64-updates-disabled-@@VERSION@@@@QUALITY@@.msi</a></td>
  </tr>
  <tr>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-win32-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-win32-x64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-win32-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-win32-x64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>CLI</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-cli-win32-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-cli-win32-x64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td rowspan="5">macOS</td>
    <td>.dmg</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@.x64.@@VERSION@@@@QUALITY@@.dmg">@@APP_NAME@@.x64.@@VERSION@@@@QUALITY@@.dmg</a></td>
  </tr>
  <tr>
    <td>.zip</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-darwin-x64-@@VERSION@@@@QUALITY@@.zip">@@APP_NAME@@-darwin-x64-@@VERSION@@@@QUALITY@@.zip</a></td>
  </tr>
  <tr>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-darwin-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-darwin-x64-@@VERSION@@.@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-darwin-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-darwin-x64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>CLI</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-cli-darwin-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-cli-darwin-x64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
  <td rowspan="8">Linux</td>
    <td>.deb</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@BINARY_NAME@@_@@VERSION@@_amd64.deb">@@BINARY_NAME@@_@@VERSION@@_amd64.deb</a></td>
  </tr>
  <tr>
    <td>.rpm</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@BINARY_NAME@@-@@VERSION@@-el8.x86_64.rpm">@@BINARY_NAME@@-@@VERSION@@-el8.x86_64.rpm</a></td>
  </tr>
  <tr>
    <td>.tar.gz</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-linux-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME@@-linux-x64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>AppImage</td>
    <td>
      <a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_QUALITY@@-@@VERSION@@.glibc2.29-x86_64.AppImage">@@APP_NAME_QUALITY@@-@@VERSION@@.glibc2.29-x86_64.AppImage</a><br />
      <a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_QUALITY@@-@@VERSION@@.glibc2.29-x86_64.AppImage.zsync">@@APP_NAME_QUALITY@@-@@VERSION@@.glibc2.29-x86_64.AppImage.zsync</a>
    </td>
  </tr>
  <tr>
    <td>Snap</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@BINARY_NAME@@_@@VERSION@@_amd64.snap">@@BINARY_NAME@@_@@VERSION@@_amd64.snap</a></td>
  </tr>
  <tr>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-linux-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-linux-x64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-linux-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-linux-x64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>CLI</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-cli-linux-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-cli-linux-x64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td rowspan="2">Alpine</td>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-alpine-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-alpine-x64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-alpine-x64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-alpine-x64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
</table>

## ARM 64bits

<table>
  <tr>
    <td rowspan="4">Windows</td>
    <td>User Installer</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@UserSetup-arm64-@@VERSION@@@@QUALITY@@.exe">@@APP_NAME@@UserSetup-arm64-@@VERSION@@@@QUALITY@@.exe</a></td>
  </tr>
  <tr>
    <td>System Installer</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@Setup-arm64-@@VERSION@@@@QUALITY@@.exe">@@APP_NAME@@Setup-arm64-@@VERSION@@@@QUALITY@@.exe</a></td>
  </tr>
  <tr>
    <td>.zip</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-win32-arm64-@@VERSION@@@@QUALITY@@.zip">@@APP_NAME@@-win32-arm64-@@VERSION@@@@QUALITY@@.zip</a></td>
  </tr>
  <tr>
    <td>CLI</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-cli-win32-arm64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-cli-win32-arm64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td rowspan="5">macOS</td>
    <td>.dmg</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@.arm64.@@VERSION@@@@QUALITY@@.dmg">@@APP_NAME@@.arm64.@@VERSION@@@@QUALITY@@.dmg</a></td>
  </tr>
  <tr>
    <td>.zip</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-darwin-arm64-@@VERSION@@@@QUALITY@@.zip">@@APP_NAME@@-darwin-arm64-@@VERSION@@@@QUALITY@@.zip</a></td>
  </tr>
  <tr>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-darwin-arm64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-darwin-arm64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-darwin-arm64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-darwin-arm64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>CLI</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-cli-darwin-arm64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-cli-darwin-arm64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td rowspan="7">Linux</td>
    <td>.deb</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@BINARY_NAME@@_@@VERSION@@_arm64.deb">@@BINARY_NAME@@_@@VERSION@@_arm64.deb</a></td>
  </tr>
  <tr>
    <td>.rpm</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@BINARY_NAME@@-@@VERSION@@-el8.aarch64.rpm">@@BINARY_NAME@@-@@VERSION@@-el8.aarch64.rpm</a></td>
  </tr>
  <tr>
    <td>.tar.gz</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-linux-arm64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME@@-linux-arm64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Snap</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@BINARY_NAME@@_@@VERSION@@_arm64.snap">@@BINARY_NAME@@_@@VERSION@@_arm64.snap</a></td>
  </tr>
  <tr>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-linux-arm64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-linux-arm64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-linux-arm64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-linux-arm64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>CLI</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-cli-linux-arm64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-cli-linux-arm64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td rowspan="2">Alpine</td>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-alpine-arm64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-alpine-arm64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-alpine-arm64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-alpine-arm64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
</table>

## ARM 32bits

<table>
  <tr>
    <td rowspan="6">Linux</td>
    <td>.deb</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@BINARY_NAME@@_@@VERSION@@_armhf.deb">@@BINARY_NAME@@_@@VERSION@@_armhf.deb</a></td>
  </tr>
  <tr>
    <td>.rpm</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@BINARY_NAME@@-@@VERSION@@-el8.armv7hl.rpm">@@BINARY_NAME@@-@@VERSION@@-el8.armv7hl.rpm</a></td>
  </tr>
  <tr>
    <td>.tar.gz</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-linux-armhf-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME@@-linux-armhf-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-linux-armhf-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-linux-armhf-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-linux-armhf-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-linux-armhf-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>CLI</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-cli-linux-armhf-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-cli-linux-armhf-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
</table>

## PPC 64bits

<table>
  <tr>
    <td rowspan="3">Linux</td>
    <td>.tar.gz</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-linux-ppc64le-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME@@-linux-ppc64le-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-linux-ppc64le-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-linux-ppc64le-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-linux-ppc64le-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-linux-ppc64le-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
</table>

## RISC-V 64bits

<table>
  <tr>
    <td rowspan="3">Linux</td>
    <td>.tar.gz</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-linux-riscv64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME@@-linux-riscv64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-linux-riscv64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-linux-riscv64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-linux-riscv64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-linux-riscv64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
</table>

## Loong 64bits

<table>
  <tr>
    <td rowspan="3">Linux</td>
    <td>.tar.gz</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME@@-linux-loong64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME@@-linux-loong64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-linux-loong64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-linux-loong64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-linux-loong64-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-linux-loong64-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
</table>

## s390x

<table>
  <tr>
    <td rowspan="2">Linux</td>
    <td>Remote Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-linux-s390x-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-linux-s390x-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
  <tr>
    <td>Web Host</td>
    <td><a href="https://github.com/@@ASSETS_REPOSITORY@@/releases/download/@@VERSION@@@@QUALITY@@/@@APP_NAME_LC@@-reh-web-linux-s390x-@@VERSION@@@@QUALITY@@.tar.gz">@@APP_NAME_LC@@-reh-web-linux-s390x-@@VERSION@@@@QUALITY@@.tar.gz</a></td>
  </tr>
</table>
