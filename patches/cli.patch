diff --git a/cli/src/commands/serve_web.rs b/cli/src/commands/serve_web.rs
index 2ddefe1..ab9c658 100644
--- a/cli/src/commands/serve_web.rs
+++ b/cli/src/commands/serve_web.rs
@@ -730,3 +730,3 @@ impl ConnectionManager {
 		let dir_fut = cache.create(&args.release.commit, |target_dir| async move {
-			info!(log_for_fut, "Downloading server {}", release_for_fut.commit);
+			info!(log_for_fut, "Downloading server {}/{}", release_for_fut.commit, release_for_fut.name);
 			let tmpdir = tempfile::tempdir().unwrap();
@@ -758,3 +758,3 @@ impl ConnectionManager {
 			.join("bin")
-			.join(args.release.quality.server_entrypoint());
+			.join(args.release.quality.server_entrypoint().unwrap());
 
diff --git a/cli/src/constants.rs b/cli/src/constants.rs
index 1e277a8..97f17d3 100644
--- a/cli/src/constants.rs
+++ b/cli/src/constants.rs
@@ -35,3 +35,6 @@ pub const DOCUMENTATION_URL: Option<&'static str> = option_env!("VSCODE_CLI_DOCU
 pub const VSCODE_CLI_COMMIT: Option<&'static str> = option_env!("VSCODE_CLI_COMMIT");
-pub const VSCODE_CLI_UPDATE_ENDPOINT: Option<&'static str> = option_env!("VSCODE_CLI_UPDATE_URL");
+pub const VSCODE_CLI_UPDATE_ENDPOINT: Option<&'static str> = option_env!("VSCODE_CLI_UPDATE_ENDPOINT");
+pub const VSCODE_CLI_DOWNLOAD_ENDPOINT: Option<&'static str> = option_env!("VSCODE_CLI_DOWNLOAD_ENDPOINT");
+pub const VSCODE_CLI_APP_NAME: Option<&'static str> = option_env!("VSCODE_CLI_APP_NAME");
+pub const VSCODE_CLI_BINARY_NAME: Option<&'static str> = option_env!("VSCODE_CLI_BINARY_NAME");
 
diff --git a/cli/src/options.rs b/cli/src/options.rs
index 7d152c0..c0f2fb2 100644
--- a/cli/src/options.rs
+++ b/cli/src/options.rs
@@ -9,3 +9,3 @@ use serde::{Deserialize, Serialize};
 
-use crate::constants::SERVER_NAME_MAP;
+use crate::{constants::VSCODE_CLI_BINARY_NAME, util::errors::CodeError};
 
@@ -21,2 +21,6 @@ pub enum Quality {
 
+fn get_binary_name() -> Result<&'static str, CodeError> {
+	VSCODE_CLI_BINARY_NAME.ok_or_else(|| CodeError::UpdatesNotConfigured("no binary name"))
+}
+
 impl Quality {
@@ -41,9 +45,4 @@ impl Quality {
 	/// Server application name
-	pub fn server_entrypoint(&self) -> String {
-		let mut server_name = SERVER_NAME_MAP
-			.as_ref()
-			.and_then(|m| m.get(self))
-			.map(|s| s.server_application_name.as_str())
-			.unwrap_or("code-server-oss")
-			.to_string();
+	pub fn server_entrypoint(&self) -> Result<String, CodeError> {
+		let mut server_name = get_binary_name()?.to_string();
 
@@ -53,3 +52,3 @@ impl Quality {
 
-		server_name
+		Ok(server_name)
 	}
diff --git a/cli/src/tunnels/code_server.rs b/cli/src/tunnels/code_server.rs
index cf00bc4..b564330 100644
--- a/cli/src/tunnels/code_server.rs
+++ b/cli/src/tunnels/code_server.rs
@@ -457,3 +457,3 @@ impl<'a> ServerBuilder<'a> {
 							.join("bin")
-							.join(self.server_params.release.quality.server_entrypoint()),
+							.join(self.server_params.release.quality.server_entrypoint().unwrap()),
 						&["--version"],
diff --git a/cli/src/tunnels/paths.rs b/cli/src/tunnels/paths.rs
index 3d7d718..98529bc 100644
--- a/cli/src/tunnels/paths.rs
+++ b/cli/src/tunnels/paths.rs
@@ -100,3 +100,3 @@ impl InstalledServer {
 					.join("bin")
-					.join(self.quality.server_entrypoint())
+					.join(self.quality.server_entrypoint().unwrap())
 			},
diff --git a/cli/src/update_service.rs b/cli/src/update_service.rs
index 9033914..a39bbf7 100644
--- a/cli/src/update_service.rs
+++ b/cli/src/update_service.rs
@@ -10,3 +10,3 @@ use serde::{Deserialize, Serialize};
 use crate::{
-	constants::VSCODE_CLI_UPDATE_ENDPOINT,
+	constants::{VSCODE_CLI_APP_NAME, VSCODE_CLI_DOWNLOAD_ENDPOINT, VSCODE_CLI_UPDATE_ENDPOINT},
 	debug, log, options, spanf,
@@ -18,3 +18,3 @@ use crate::{
 		zipper,
-	},
+	}
 };
@@ -58,4 +58,12 @@ fn quality_download_segment(quality: options::Quality) -> &'static str {
 
+fn get_app_name() -> Result<&'static str, CodeError> {
+	VSCODE_CLI_APP_NAME.ok_or_else(|| CodeError::UpdatesNotConfigured("no app name"))
+}
+
+fn get_download_endpoint() -> Result<&'static str, CodeError> {
+	VSCODE_CLI_DOWNLOAD_ENDPOINT.ok_or_else(|| CodeError::UpdatesNotConfigured("no download url"))
+}
+
 fn get_update_endpoint() -> Result<&'static str, CodeError> {
-	VSCODE_CLI_UPDATE_ENDPOINT.ok_or_else(|| CodeError::UpdatesNotConfigured("no service url"))
+	VSCODE_CLI_UPDATE_ENDPOINT.ok_or_else(|| CodeError::UpdatesNotConfigured("no update url"))
 }
@@ -67,3 +75,4 @@ impl UpdateService {
 
-	pub async fn get_release_by_semver_version(
+	/// Gets the latest commit for the target of the given quality.
+	pub async fn get_latest_commit(
 		&self,
@@ -72,14 +81,10 @@ impl UpdateService {
 		quality: options::Quality,
-		version: &str,
 	) -> Result<Release, AnyError> {
 		let update_endpoint = get_update_endpoint()?;
-		let download_segment = target
-			.download_segment(platform)
-			.ok_or_else(|| CodeError::UnsupportedPlatform(platform.to_string()))?;
 		let download_url = format!(
-			"{}/api/versions/{}/{}/{}",
+			"{}/{}/{}/{}/latest.json",
 			update_endpoint,
-			version,
-			download_segment,
 			quality_download_segment(quality),
+			platform.os(),
+			platform.arch(),
 		);
@@ -97,3 +102,3 @@ impl UpdateService {
 		let res = response.json::<UpdateServerVersion>().await?;
-		debug!(self.log, "Resolved version {} to {}", version, res.version);
+		debug!(self.log, "Resolved quality {} to {}", quality, res.version);
 
@@ -108,40 +113,17 @@ impl UpdateService {
 
-	/// Gets the latest commit for the target of the given quality.
-	pub async fn get_latest_commit(
-		&self,
-		platform: Platform,
-		target: TargetKind,
-		quality: options::Quality,
-	) -> Result<Release, AnyError> {
-		let update_endpoint = get_update_endpoint()?;
-		let download_segment = target
-			.download_segment(platform)
-			.ok_or_else(|| CodeError::UnsupportedPlatform(platform.to_string()))?;
+	pub fn get_download_url(&self, release: &Release) -> Result<String, AnyError> {
+		let app_name = get_app_name()?;
+		let download_endpoint = get_download_endpoint()?;
+
 		let download_url = format!(
-			"{}/api/latest/{}/{}",
-			update_endpoint,
-			download_segment,
-			quality_download_segment(quality),
+			"{}/download/{}/{}-reh-web-{}-{}-{}.tar.gz",
+			download_endpoint,
+			release.name,
+			app_name,
+			release.platform.os(),
+			release.platform.arch(),
+			release.name,
 		);
 
-		let mut response = spanf!(
-			self.log,
-			self.log.span("server.version.resolve"),
-			self.client.make_request("GET", download_url)
-		)?;
-
-		if !response.status_code.is_success() {
-			return Err(response.into_err().await.into());
-		}
-
-		let res = response.json::<UpdateServerVersion>().await?;
-		debug!(self.log, "Resolved quality {} to {}", quality, res.version);
-
-		Ok(Release {
-			target,
-			platform,
-			quality,
-			name: res.name,
-			commit: res.version,
-		})
+		Ok(download_url)
 	}
@@ -150,15 +132,3 @@ impl UpdateService {
 	pub async fn get_download_stream(&self, release: &Release) -> Result<SimpleResponse, AnyError> {
-		let update_endpoint = get_update_endpoint()?;
-		let download_segment = release
-			.target
-			.download_segment(release.platform)
-			.ok_or_else(|| CodeError::UnsupportedPlatform(release.platform.to_string()))?;
-
-		let download_url = format!(
-			"{}/commit:{}/{}/{}",
-			update_endpoint,
-			release.commit,
-			download_segment,
-			quality_download_segment(release.quality),
-		);
+		let download_url = self.get_download_url(release)?;
 
@@ -196,13 +166,2 @@ pub enum TargetKind {
 
-impl TargetKind {
-	fn download_segment(&self, platform: Platform) -> Option<String> {
-		match *self {
-			TargetKind::Server => Some(platform.headless()),
-			TargetKind::Archive => platform.archive(),
-			TargetKind::Web => Some(platform.web()),
-			TargetKind::Cli => Some(platform.cli()),
-		}
-	}
-}
-
 #[derive(Debug, Copy, Clone, Eq, PartialEq, Serialize, Deserialize)]
@@ -225,30 +184,17 @@ pub enum Platform {
 impl Platform {
-	pub fn archive(&self) -> Option<String> {
-		match self {
-			Platform::LinuxX64 => Some("linux-x64".to_owned()),
-			Platform::LinuxARM64 => Some("linux-arm64".to_owned()),
-			Platform::LinuxARM32 => Some("linux-armhf".to_owned()),
-			Platform::DarwinX64 => Some("darwin".to_owned()),
-			Platform::DarwinARM64 => Some("darwin-arm64".to_owned()),
-			Platform::WindowsX64 => Some("win32-x64-archive".to_owned()),
-			Platform::WindowsX86 => Some("win32-archive".to_owned()),
-			Platform::WindowsARM64 => Some("win32-arm64-archive".to_owned()),
-			_ => None,
-		}
-	}
-	pub fn headless(&self) -> String {
+	pub fn arch(&self) -> String {
 		match self {
-			Platform::LinuxAlpineARM64 => "server-alpine-arm64",
-			Platform::LinuxAlpineX64 => "server-linux-alpine",
-			Platform::LinuxX64 => "server-linux-x64",
-			Platform::LinuxX64Legacy => "server-linux-legacy-x64",
-			Platform::LinuxARM64 => "server-linux-arm64",
-			Platform::LinuxARM64Legacy => "server-linux-legacy-arm64",
-			Platform::LinuxARM32 => "server-linux-armhf",
-			Platform::LinuxARM32Legacy => "server-linux-legacy-armhf",
-			Platform::DarwinX64 => "server-darwin",
-			Platform::DarwinARM64 => "server-darwin-arm64",
-			Platform::WindowsX64 => "server-win32-x64",
-			Platform::WindowsX86 => "server-win32",
-			Platform::WindowsARM64 => "server-win32-arm64",
+			Platform::LinuxAlpineARM64 => "arm64",
+			Platform::LinuxAlpineX64 => "x64",
+			Platform::LinuxX64 => "x64",
+			Platform::LinuxX64Legacy => "x64",
+			Platform::LinuxARM64 => "arm64",
+			Platform::LinuxARM64Legacy => "arm64",
+			Platform::LinuxARM32 => "armhf",
+			Platform::LinuxARM32Legacy => "armhf",
+			Platform::DarwinX64 => "x64",
+			Platform::DarwinARM64 => "arm64",
+			Platform::WindowsX64 => "x64",
+			Platform::WindowsX86 => "ia42",
+			Platform::WindowsARM64 => "arm64",
 		}
@@ -257,17 +203,17 @@ impl Platform {
 
-	pub fn cli(&self) -> String {
+	pub fn os(&self) -> String {
 		match self {
-			Platform::LinuxAlpineARM64 => "cli-alpine-arm64",
-			Platform::LinuxAlpineX64 => "cli-alpine-x64",
-			Platform::LinuxX64 => "cli-linux-x64",
-			Platform::LinuxX64Legacy => "cli-linux-x64",
-			Platform::LinuxARM64 => "cli-linux-arm64",
-			Platform::LinuxARM64Legacy => "cli-linux-arm64",
-			Platform::LinuxARM32 => "cli-linux-armhf",
-			Platform::LinuxARM32Legacy => "cli-linux-armhf",
-			Platform::DarwinX64 => "cli-darwin-x64",
-			Platform::DarwinARM64 => "cli-darwin-arm64",
-			Platform::WindowsARM64 => "cli-win32-arm64",
-			Platform::WindowsX64 => "cli-win32-x64",
-			Platform::WindowsX86 => "cli-win32",
+			Platform::LinuxAlpineARM64 => "alpine",
+			Platform::LinuxAlpineX64 => "alpine",
+			Platform::LinuxX64 => "linux",
+			Platform::LinuxX64Legacy => "linux",
+			Platform::LinuxARM64 => "linux",
+			Platform::LinuxARM64Legacy => "linux",
+			Platform::LinuxARM32 => "linux",
+			Platform::LinuxARM32Legacy => "linux",
+			Platform::DarwinX64 => "darwin",
+			Platform::DarwinARM64 => "darwin",
+			Platform::WindowsX64 => "win32",
+			Platform::WindowsX86 => "win32",
+			Platform::WindowsARM64 => "win32",
 		}
@@ -276,6 +222,2 @@ impl Platform {
 
-	pub fn web(&self) -> String {
-		format!("{}-web", self.headless())
-	}
-
 	pub fn env_default() -> Option<Platform> {
