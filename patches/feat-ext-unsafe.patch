diff --git a/src/vs/platform/extensionManagement/common/extensionGalleryService.ts b/src/vs/platform/extensionManagement/common/extensionGalleryService.ts
index 5974768..876f779 100644
--- a/src/vs/platform/extensionManagement/common/extensionGalleryService.ts
+++ b/src/vs/platform/extensionManagement/common/extensionGalleryService.ts
@@ -1788,2 +1788,8 @@ export abstract class AbstractExtensionGalleryService implements IExtensionGalle
 
+		const excludeUnsafes = this.configurationService.getValue('extensions.excludeUnsafes') ?? true;
+
+		if (!excludeUnsafes) {
+			return { malicious: [], deprecated: {}, search: [] };
+		}
+
 		const context = await this.requestService.request({
diff --git a/src/vs/workbench/contrib/extensions/browser/extensions.contribution.ts b/src/vs/workbench/contrib/extensions/browser/extensions.contribution.ts
index 4c44d3b..fe989e0 100644
--- a/src/vs/workbench/contrib/extensions/browser/extensions.contribution.ts
+++ b/src/vs/workbench/contrib/extensions/browser/extensions.contribution.ts
@@ -294,2 +294,9 @@ Registry.as<IConfigurationRegistry>(ConfigurationExtensions.Configuration)
 			},
+			'extensions.excludeUnsafes': {
+				scope: ConfigurationScope.MACHINE,
+				type: 'boolean',
+				default: true,
+				description: localize('extensionsExcludeUnsafes', "When enabled, it will download and cache the list of malicious and deprecated extensions. It's recommended to leave it enabled."),
+				tags: ['usesOnlineServices']
+			},
 		}
