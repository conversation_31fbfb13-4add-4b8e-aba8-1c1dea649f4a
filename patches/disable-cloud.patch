diff --git a/src/vs/workbench/contrib/editSessions/browser/editSessionsStorageService.ts b/src/vs/workbench/contrib/editSessions/browser/editSessionsStorageService.ts
index 1bb5eb3..70f1b37 100644
--- a/src/vs/workbench/contrib/editSessions/browser/editSessionsStorageService.ts
+++ b/src/vs/workbench/contrib/editSessions/browser/editSessionsStorageService.ts
@@ -7,3 +7,3 @@ import { Disposable, DisposableStore } from '../../../../base/common/lifecycle.j
 import { localize } from '../../../../nls.js';
-import { Action2, MenuId, MenuRegistry, registerAction2 } from '../../../../platform/actions/common/actions.js';
+import { Action2, MenuId, registerAction2 } from '../../../../platform/actions/common/actions.js';
 import { ContextKeyExpr, I<PERSON>ontext<PERSON>ey, IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';
@@ -17,3 +17,3 @@ import { AuthenticationSession, AuthenticationSessionsChangeEvent, IAuthenticati
 import { IExtensionService } from '../../../services/extensions/common/extensions.js';
-import { EDIT_SESSIONS_SIGNED_IN, EditSession, EDIT_SESSION_SYNC_CATEGORY, IEditSessionsStorageService, EDIT_SESSIONS_SIGNED_IN_KEY, IEditSessionsLogService, SyncResource, EDIT_SESSIONS_PENDING_KEY } from '../common/editSessions.js';
+import { EDIT_SESSIONS_SIGNED_IN, EditSession, EDIT_SESSION_SYNC_CATEGORY, IEditSessionsStorageService, EDIT_SESSIONS_SIGNED_IN_KEY, IEditSessionsLogService, SyncResource } from '../common/editSessions.js';
 import { IDialogService } from '../../../../platform/dialogs/common/dialogs.js';
@@ -93,3 +93,2 @@ export class EditSessionsWorkbenchService extends Disposable implements IEditSes
 
-		this.registerSignInAction();
 		this.registerResetAuthenticationAction();
@@ -456,42 +455,2 @@ export class EditSessionsWorkbenchService extends Disposable implements IEditSes
 
-	private registerSignInAction() {
-		if (!this.serverConfiguration?.url) {
-			return;
-		}
-		const that = this;
-		const id = 'workbench.editSessions.actions.signIn';
-		const when = ContextKeyExpr.and(ContextKeyExpr.equals(EDIT_SESSIONS_PENDING_KEY, false), ContextKeyExpr.equals(EDIT_SESSIONS_SIGNED_IN_KEY, false));
-		this._register(registerAction2(class ResetEditSessionAuthenticationAction extends Action2 {
-			constructor() {
-				super({
-					id,
-					title: localize('sign in', 'Turn on Cloud Changes...'),
-					category: EDIT_SESSION_SYNC_CATEGORY,
-					precondition: when,
-					menu: [{
-						id: MenuId.CommandPalette,
-					},
-					{
-						id: MenuId.AccountsContext,
-						group: '2_editSessions',
-						when,
-					}]
-				});
-			}
-
-			async run() {
-				return await that.initialize('write', false);
-			}
-		}));
-
-		this._register(MenuRegistry.appendMenuItem(MenuId.AccountsContext, {
-			group: '2_editSessions',
-			command: {
-				id,
-				title: localize('sign in badge', 'Turn on Cloud Changes... (1)'),
-			},
-			when: ContextKeyExpr.and(ContextKeyExpr.equals(EDIT_SESSIONS_PENDING_KEY, true), ContextKeyExpr.equals(EDIT_SESSIONS_SIGNED_IN_KEY, false))
-		}));
-	}
-
 	private registerResetAuthenticationAction() {
