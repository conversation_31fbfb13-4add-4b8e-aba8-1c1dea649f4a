diff --git a/extensions/github-authentication/src/common/env.ts b/extensions/github-authentication/src/common/env.ts
index ebc4749..18fd732 100644
--- a/extensions/github-authentication/src/common/env.ts
+++ b/extensions/github-authentication/src/common/env.ts
@@ -7,21 +7,4 @@ import { AuthProviderType } from '../github';
 
-const VALID_DESKTOP_CALLBACK_SCHEMES = [
-	'vscode',
-	'vscode-insiders',
-	// On Windows, some browsers don't seem to redirect back to OSS properly.
-	// As a result, you get stuck in the auth flow. We exclude this from the
-	// list until we can figure out a way to fix this behavior in browsers.
-	// 'code-oss',
-	'vscode-wsl',
-	'vscode-exploration'
-];
-
-export function isSupportedClient(uri: Uri): boolean {
-	return (
-		VALID_DESKTOP_CALLBACK_SCHEMES.includes(uri.scheme) ||
-		// vscode.dev & insiders.vscode.dev
-		/(?:^|\.)vscode\.dev$/.test(uri.authority) ||
-		// github.dev & codespaces
-		/(?:^|\.)github\.dev$/.test(uri.authority)
-	);
+export function isSupportedClient(_uri: Uri): boolean {
+	return false;
 }
@@ -35,4 +18,4 @@ export function isSupportedTarget(type: AuthProviderType, gheUri?: Uri): boolean
 
-export function isHostedGitHubEnterprise(uri: Uri): boolean {
-	return /\.ghe\.com$/.test(uri.authority);
+export function isHostedGitHubEnterprise(_uri: Uri): boolean {
+	return false;
 }
