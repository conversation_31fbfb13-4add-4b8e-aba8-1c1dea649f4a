diff --git a/remote/package-lock.json b/remote/package-lock.json
index f0ed1cc..a973af3 100644
--- a/remote/package-lock.json
+++ b/remote/package-lock.json
@@ -40,2 +40,3 @@
         "tas-client-umd": "0.2.0",
+        "tslib": "^2.6.3",
         "vscode-oniguruma": "1.7.0",
@@ -1109,2 +1110,8 @@
     },
+    "node_modules/tslib": {
+      "version": "2.7.0",
+      "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.7.0.tgz",
+      "integrity": "sha512-gLXCKdN1/j47AiHiOkJN69hJmcbGTHI0ImLmbYLHykhgeN0jVGola9yVjFgzCUklsZQMW55o+dW7IXv3RCXDzA==",
+      "license": "0BSD"
+    },
     "node_modules/tunnel-agent": {
diff --git a/remote/package.json b/remote/package.json
index 33afee9..525ed07 100644
--- a/remote/package.json
+++ b/remote/package.json
@@ -35,2 +35,3 @@
     "tas-client-umd": "0.2.0",
+    "tslib": "^2.6.3",
     "vscode-oniguruma": "1.7.0",
