diff --git a/build/lib/compilation.js b/build/lib/compilation.js
index 841dbe1..7cf692a 100644
--- a/build/lib/compilation.js
+++ b/build/lib/compilation.js
@@ -148,24 +148,3 @@ function compileTask(src, out, build, options = {}) {
         }
-        // mangle: TypeScript to TypeScript
-        let mangleStream = event_stream_1.default.through();
-        if (build && !options.disableMangle) {
-            let ts2tsMangler = new index_1.Mangler(compile.projectPath, (...data) => (0, fancy_log_1.default)(ansi_colors_1.default.blue('[mangler]'), ...data), { mangleExports: true, manglePrivateFields: true });
-            const newContentsByFileName = ts2tsMangler.computeNewFileContents(new Set(['saveState']));
-            mangleStream = event_stream_1.default.through(async function write(data) {
-                const tsNormalPath = ts.normalizePath(data.path);
-                const newContents = (await newContentsByFileName).get(tsNormalPath);
-                if (newContents !== undefined) {
-                    data.contents = Buffer.from(newContents.out);
-                    data.sourceMap = newContents.sourceMap && JSON.parse(newContents.sourceMap);
-                }
-                this.push(data);
-            }, async function end() {
-                // free resources
-                (await newContentsByFileName).clear();
-                this.push(null);
-                ts2tsMangler = undefined;
-            });
-        }
         return srcPipe
-            .pipe(mangleStream)
             .pipe(generator.stream)
diff --git a/build/lib/compilation.ts b/build/lib/compilation.ts
index 6e1fcab..4e45106 100644
--- a/build/lib/compilation.ts
+++ b/build/lib/compilation.ts
@@ -18,4 +18,2 @@ import File from 'vinyl';
 import * as task from './task';
-import { Mangler } from './mangle/index';
-import { RawSourceMap } from 'source-map';
 import { gulpPostcss } from './postcss';
@@ -138,27 +136,3 @@ export function compileTask(src: string, out: string, build: boolean, options: {
 
-		// mangle: TypeScript to TypeScript
-		let mangleStream = es.through();
-		if (build && !options.disableMangle) {
-			let ts2tsMangler = new Mangler(compile.projectPath, (...data) => fancyLog(ansiColors.blue('[mangler]'), ...data), { mangleExports: true, manglePrivateFields: true });
-			const newContentsByFileName = ts2tsMangler.computeNewFileContents(new Set(['saveState']));
-			mangleStream = es.through(async function write(data: File & { sourceMap?: RawSourceMap }) {
-				type TypeScriptExt = typeof ts & { normalizePath(path: string): string };
-				const tsNormalPath = (<TypeScriptExt>ts).normalizePath(data.path);
-				const newContents = (await newContentsByFileName).get(tsNormalPath);
-				if (newContents !== undefined) {
-					data.contents = Buffer.from(newContents.out);
-					data.sourceMap = newContents.sourceMap && JSON.parse(newContents.sourceMap);
-				}
-				this.push(data);
-			}, async function end() {
-				// free resources
-				(await newContentsByFileName).clear();
-
-				this.push(null);
-				(<any>ts2tsMangler) = undefined;
-			});
-		}
-
 		return srcPipe
-			.pipe(mangleStream)
 			.pipe(generator.stream)
