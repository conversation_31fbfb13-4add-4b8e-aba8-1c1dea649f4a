diff --git a/src/vs/workbench/electron-sandbox/window.ts b/src/vs/workbench/electron-sandbox/window.ts
index 90c9654..3ef5d32 100644
--- a/src/vs/workbench/electron-sandbox/window.ts
+++ b/src/vs/workbench/electron-sandbox/window.ts
@@ -257,4 +257,4 @@ export class NativeWindow extends BaseWindow {
 						const quality = this.productService.quality;
-						const stableURL = 'https://code.visualstudio.com/docs/?dv=osx';
-						const insidersURL = 'https://code.visualstudio.com/docs/?dv=osx&build=insiders';
+						const stableURL = 'https://github.com/!!GH_REPO_PATH!!/releases/latest';
+						const insidersURL = 'https://github.com/!!GH_REPO_PATH!!-insiders/releases/latest';
 						this.openerService.open(quality === 'stable' ? stableURL : insidersURL);
