diff --git a/build/darwin/sign.js b/build/darwin/sign.js
index dff30fd..df48bee 100644
--- a/build/darwin/sign.js
+++ b/build/darwin/sign.js
@@ -56,5 +56,7 @@ async function main(buildDir) {
         ignore: (filePath) => {
+            const ext = path_1.default.extname(filePath);
             return filePath.includes(gpuHelperAppName) ||
                 filePath.includes(rendererHelperAppName) ||
-                filePath.includes(pluginHelperAppName);
+                filePath.includes(pluginHelperAppName) ||
+                ext == '.asar' || ext == '.dat' || ext == '.gif' || ext == '.icns' || ext == '.ico' || ext == '.json' || ext == '.mp3' || ext == '.nib' || ext == '.pak' || ext == '.png' || ext == '.scpt' || ext == '.ttf' || ext == '.wasm' || ext == '.woff' || ext == '.woff2';
         }
diff --git a/build/darwin/sign.ts b/build/darwin/sign.ts
index ecf1627..a414032 100644
--- a/build/darwin/sign.ts
+++ b/build/darwin/sign.ts
@@ -60,6 +60,9 @@ async function main(buildDir?: string): Promise<void> {
 		ignore: (filePath: string) => {
-			return filePath.includes(gpuHelperAppName) ||
-				filePath.includes(rendererHelperAppName) ||
-				filePath.includes(pluginHelperAppName);
+			const ext = path.extname(filePath);
+            return filePath.includes(gpuHelperAppName) ||
+                filePath.includes(rendererHelperAppName) ||
+                filePath.includes(pluginHelperAppName) ||
+				ext == '.asar' || ext == '.dat' || ext == '.gif' || ext == '.icns' || ext == '.ico' || ext == '.json' || ext == '.mp3' || ext == '.nib' || ext == '.pak' || ext == '.png' || ext == '.scpt' || ext == '.ttf' || ext == '.wasm' || ext == '.woff' || ext == '.woff2';
 		}
+
 	};
