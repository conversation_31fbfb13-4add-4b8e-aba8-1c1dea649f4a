--- vscode-1.80.1/extensions/mangle-loader.js.old	2023-07-12 18:45:52.000000000 +0200
+++ vscode-1.80.1/extensions/mangle-loader.js	2023-07-28 11:55:01.177444702 +0200
@@ -37,7 +37,7 @@ function getMangledFileContents(projectP
  * @type {webpack.LoaderDefinitionFunction}
  */
 module.exports = async function (source, sourceMap, meta) {
-	if (this.mode !== 'production') {
+	if (true) {
 		// Only enable mangling in production builds
 		return source;
 	}
