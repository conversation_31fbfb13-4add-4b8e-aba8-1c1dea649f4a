diff --git a/build/package-lock.json b/build/package-lock.json
index 445e842..1dea5d1 100644
--- a/build/package-lock.json
+++ b/build/package-lock.json
@@ -59,3 +59,2 @@
         "through2": "^4.0.2",
-        "tree-sitter": "^0.22.4",
         "vscode-universal-bundler": "^0.1.3",
@@ -65,2 +64,3 @@
       "optionalDependencies": {
+        "tree-sitter": "^0.22.4",
         "tree-sitter-typescript": "^0.23.2",
@@ -3416,4 +3416,4 @@
       "integrity": "sha512-LA4ZjwlnUblHVgq0oBF3Jl/6h/Nvs5fzBLwdEF4nuxnFdsfajde4WfxtJr3CaiH+F6ewcIB/q4jQ4UzPyid+CQ==",
-      "devOptional": true,
       "license": "MIT",
+      "optional": true,
       "bin": {
@@ -4243,5 +4243,5 @@
       "integrity": "sha512-usbHZP9/oxNsUY65MQUsduGRqDHQOou1cagUSwjhoSYAmSahjQDAVsh9s+SlZkn8X8+O1FULRGwHu7AFP3kjzg==",
-      "dev": true,
       "hasInstallScript": true,
       "license": "MIT",
+      "optional": true,
       "dependencies": {
@@ -4316,4 +4316,4 @@
       "integrity": "sha512-lytcDEdxKjGJPTLEfW4mYMigRezMlyJY8W4wxJK8zE533Jlb8L8dRuObJFWg2P+AuOIxoCgKF+2Oq4d4Zd0OUA==",
-      "dev": true,
       "license": "MIT",
+      "optional": true,
       "engines": {
diff --git a/build/package.json b/build/package.json
index 73d4f42..0eaf964 100644
--- a/build/package.json
+++ b/build/package.json
@@ -53,3 +53,2 @@
     "through2": "^4.0.2",
-    "tree-sitter": "^0.22.4",
     "vscode-universal-bundler": "^0.1.3",
@@ -65,2 +64,3 @@
   "optionalDependencies": {
+    "tree-sitter": "^0.22.4",
     "tree-sitter-typescript": "^0.23.2",
