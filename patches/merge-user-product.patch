diff --git a/src/main.ts b/src/main.ts
index fdc424e..46b014b 100644
--- a/src/main.ts
+++ b/src/main.ts
@@ -8,2 +8,3 @@ import * as fs from 'original-fs';
 import * as os from 'os';
+import { createRequire } from 'node:module';
 import { performance } from 'perf_hooks';
@@ -24,2 +25,3 @@ import { NativeParsedArgs } from './vs/platform/environment/common/argv.js';
 const __dirname = path.dirname(fileURLToPath(import.meta.url));
+const require = createRequire(import.meta.url);
 
@@ -112,2 +114,14 @@ registerListeners();
 
+function resolveUserProduct() {
+	const userProductPath = path.join(userDataPath, 'product.json');
+
+	try {
+		// Assign the product configuration to the global scope
+		const productJson = require(userProductPath);
+		// @ts-expect-error
+		globalThis._VSCODE_USER_PRODUCT_JSON = productJson;
+	} catch (ex) {
+	}
+}
+
 /**
@@ -208,2 +222,3 @@ async function startup(codeCachePath: string | undefined, nlsConfig: INLSConfigu
 	process.env['VSCODE_CODE_CACHE_PATH'] = codeCachePath || '';
+	resolveUserProduct();
 
diff --git a/src/vs/platform/product/common/product.ts b/src/vs/platform/product/common/product.ts
index d27cdce..d758c5e 100644
--- a/src/vs/platform/product/common/product.ts
+++ b/src/vs/platform/product/common/product.ts
@@ -31,2 +31,36 @@ else if (globalThis._VSCODE_PRODUCT_JSON && globalThis._VSCODE_PACKAGE_JSON) {
 
+	const { serviceUrl, controlUrl, extensionUrlTemplate, resourceUrlTemplate } = product.extensionsGallery || {};
+
+	Object.assign(product, {
+		extensionsGallery: {
+			serviceUrl: env['VSCODE_GALLERY_SERVICE_URL'] || serviceUrl,
+			controlUrl: env['VSCODE_GALLERY_CONTROL_URL'] || controlUrl,
+			extensionUrlTemplate: env['VSCODE_GALLERY_EXTENSION_URL_TEMPLATE'] || extensionUrlTemplate,
+			resourceUrlTemplate: env['VSCODE_GALLERY_RESOURCE_URL_TEMPLATE'] || resourceUrlTemplate,
+		}
+	});
+
+	// Merge user-customized product.json
+	try {
+		const merge = (...objects: any[]) =>
+			objects.reduce((result, current) => {
+				Object.keys(current).forEach((key) => {
+					if (Array.isArray(result[key]) && Array.isArray(current[key])) {
+						result[key] = current[key];
+					} else if (typeof result[key] === 'object' && typeof current[key] === 'object') {
+						result[key] = merge(result[key], current[key]);
+					} else {
+						result[key] = current[key];
+					}
+				});
+
+				return result;
+			}, {}) as any;
+
+		const userProduct = (globalThis as Record<string, any>)._VSCODE_USER_PRODUCT_JSON || {};
+
+		product = merge(product, userProduct);
+	} catch (ex) {
+	}
+
 	// Running out of sources
