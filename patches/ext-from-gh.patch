diff --git a/build/lib/builtInExtensions.js b/build/lib/builtInExtensions.js
index 249777c..129af8a 100644
--- a/build/lib/builtInExtensions.js
+++ b/build/lib/builtInExtensions.js
@@ -87,5 +87,2 @@ function getExtensionDownloadStream(extension) {
     }
-    else if (productjson.extensionsGallery?.serviceUrl) {
-        input = ext.fromMarketplace(productjson.extensionsGallery.serviceUrl, extension);
-    }
     else {
diff --git a/build/lib/builtInExtensions.ts b/build/lib/builtInExtensions.ts
index e9a1180..b8348e3 100644
--- a/build/lib/builtInExtensions.ts
+++ b/build/lib/builtInExtensions.ts
@@ -75,4 +75,2 @@ function getExtensionDownloadStream(extension: IExtensionDefinition) {
 		input = ext.fromVsix(path.join(root, extension.vsix), extension);
-	} else if (productjson.extensionsGallery?.serviceUrl) {
-		input = ext.fromMarketplace(productjson.extensionsGallery.serviceUrl, extension);
 	} else {
