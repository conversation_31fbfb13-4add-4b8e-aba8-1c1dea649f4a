diff --git a/build/azure-pipelines/linux/setup-env.sh b/build/azure-pipelines/linux/setup-env.sh
index a148d51..fb2f8ff 100755
--- a/build/azure-pipelines/linux/setup-env.sh
+++ b/build/azure-pipelines/linux/setup-env.sh
@@ -2,3 +2,3 @@
 
-set -e
+set -ex
 
@@ -22,3 +22,3 @@ else
   echo "Downloading remote sysroot"
-  SYSROOT_ARCH="$SYSROOT_ARCH" VSCODE_SYSROOT_DIR="$VSCODE_REMOTE_SYSROOT_DIR" VSCODE_SYSROOT_PREFIX="-glibc-2.28-gcc-8.5.0" node -e '(async () => { const { getVSCodeSysroot } = require("./build/linux/debian/install-sysroot.js"); await getVSCodeSysroot(process.env["SYSROOT_ARCH"]); })()'
+  SYSROOT_ARCH="$SYSROOT_ARCH" VSCODE_SYSROOT_DIR="$VSCODE_REMOTE_SYSROOT_DIR" node -e '(async () => { const { getVSCodeSysroot } = require("./build/linux/debian/install-sysroot.js"); await getVSCodeSysroot(process.env["SYSROOT_ARCH"]); })()'
 fi
diff --git a/build/gulpfile.reh.js b/build/gulpfile.reh.js
index c1d64c0..5ac9f95 100644
--- a/build/gulpfile.reh.js
+++ b/build/gulpfile.reh.js
@@ -232,9 +232,23 @@ function nodejs(platform, arch) {
 		case 'linux':
-			return (product.nodejsRepository !== 'https://nodejs.org' ?
-				fetchGithub(product.nodejsRepository, { version: `${nodeVersion}-${internalNodeVersion}`, name: expectedName, checksumSha256 }) :
-				fetchUrls(`/dist/v${nodeVersion}/node-v${nodeVersion}-${platform}-${arch}.tar.gz`, { base: 'https://nodejs.org', checksumSha256 })
-			).pipe(flatmap(stream => stream.pipe(gunzip()).pipe(untar())))
-				.pipe(filter('**/node'))
-				.pipe(util.setExecutableBit('**'))
-				.pipe(rename('node'));
+			if (process.env.VSCODE_NODEJS_SITE && process.env.VSCODE_NODEJS_URLROOT) {
+				return fetchUrls(`${process.env.VSCODE_NODEJS_URLROOT}/v${nodeVersion}/node-v${nodeVersion}-${platform}-${arch}${process.env.VSCODE_NODEJS_URLSUFFIX}.tar.gz`, { base: process.env.VSCODE_NODEJS_SITE, checksumSha256 })
+					.pipe(flatmap(stream => stream.pipe(gunzip()).pipe(untar())))
+					.pipe(filter('**/node'))
+					.pipe(util.setExecutableBit('**'))
+					.pipe(rename('node'));
+			}
+			if (product.nodejsRepository !== 'https://nodejs.org') {
+				return fetchGithub(product.nodejsRepository, { version: `${nodeVersion}-${internalNodeVersion}`, name: expectedName, checksumSha256 })
+					.pipe(flatmap(stream => stream.pipe(gunzip()).pipe(untar())))
+					.pipe(filter('**/node'))
+					.pipe(util.setExecutableBit('**'))
+					.pipe(rename('node'));
+			}
+			else {
+				return fetchUrls(`/dist/v${nodeVersion}/node-v${nodeVersion}-${platform}-${arch}.tar.gz`, { base: 'https://nodejs.org', checksumSha256 })
+					.pipe(flatmap(stream => stream.pipe(gunzip()).pipe(untar())))
+					.pipe(filter('**/node'))
+					.pipe(util.setExecutableBit('**'))
+					.pipe(rename('node'));
+			}
 		case 'alpine':
diff --git a/build/gulpfile.vscode.js b/build/gulpfile.vscode.js
index 26a6287..004417f 100644
--- a/build/gulpfile.vscode.js
+++ b/build/gulpfile.vscode.js
@@ -366,2 +366,12 @@ function packageTask(platform, arch, sourceFolderName, destinationFolderName, op
 
+		const electronOverride = {};
+		if (process.env.VSCODE_ELECTRON_REPOSITORY) {
+			// official electron doesn't support all arch, override the repo with `VSCODE_ELECTRON_REPOSITORY`.
+			electronOverride.repo = process.env.VSCODE_ELECTRON_REPOSITORY;
+		}
+
+		if (process.env.VSCODE_ELECTRON_TAG) {
+			electronOverride.tag = process.env.VSCODE_ELECTRON_TAG;
+		}
+
 		let result = all
@@ -370,3 +380,3 @@ function packageTask(platform, arch, sourceFolderName, destinationFolderName, op
 			.pipe(filter(['**', '!**/.github/**'], { dot: true })) // https://github.com/microsoft/vscode/issues/116523
-			.pipe(electron({ ...config, platform, arch: arch === 'armhf' ? 'arm' : arch, ffmpegChromium: false }))
+			.pipe(electron({ ...config, ...electronOverride, platform, arch: arch === 'armhf' ? 'arm' : arch, ffmpegChromium: false }))
 			.pipe(filter(['**', '!LICENSE', '!version'], { dot: true }));
diff --git a/build/linux/debian/dep-lists.js b/build/linux/debian/dep-lists.js
index 4ef448d..057961c 100644
--- a/build/linux/debian/dep-lists.js
+++ b/build/linux/debian/dep-lists.js
@@ -140,3 +140,3 @@ exports.referenceGeneratedDepsByArch = {
         'xdg-utils (>= 1.0.2)'
-    ]
+    ],
 };
diff --git a/build/linux/debian/dep-lists.ts b/build/linux/debian/dep-lists.ts
index 5b7ccd5..707129f 100644
--- a/build/linux/debian/dep-lists.ts
+++ b/build/linux/debian/dep-lists.ts
@@ -140,3 +140,3 @@ export const referenceGeneratedDepsByArch = {
 		'xdg-utils (>= 1.0.2)'
-	]
+	],
 };
diff --git a/build/linux/debian/install-sysroot.js b/build/linux/debian/install-sysroot.js
index 612d0a3..8fbdf3d 100644
--- a/build/linux/debian/install-sysroot.js
+++ b/build/linux/debian/install-sysroot.js
@@ -74,3 +74,5 @@ async function fetchUrl(options, retries = 10, retryDelay = 1000) {
         try {
-            const response = await fetch(`https://api.github.com/repos/Microsoft/vscode-linux-build-agent/releases/tags/v${version}`, {
+            const repository = process.env['VSCODE_SYSROOT_REPOSITORY'] ?? 'Microsoft/vscode-linux-build-agent';
+            const actualVersion = process.env['VSCODE_SYSROOT_VERSION'] ?? version;
+            const response = await fetch(`https://api.github.com/repos/${repository}/releases/tags/v${actualVersion}`, {
                 headers: ghApiHeaders,
@@ -83,3 +85,3 @@ async function fetchUrl(options, retries = 10, retryDelay = 1000) {
                 if (!asset) {
-                    throw new Error(`Could not find asset in release of Microsoft/vscode-linux-build-agent @ ${version}`);
+                    throw new Error(`Could not find asset in release of ${repository} @ ${actualVersion}`);
                 }
diff --git a/build/linux/debian/install-sysroot.ts b/build/linux/debian/install-sysroot.ts
index 8a61159..9f3fd15 100644
--- a/build/linux/debian/install-sysroot.ts
+++ b/build/linux/debian/install-sysroot.ts
@@ -83,3 +83,5 @@ async function fetchUrl(options: IFetchOptions, retries = 10, retryDelay = 1000)
 		try {
-			const response = await fetch(`https://api.github.com/repos/Microsoft/vscode-linux-build-agent/releases/tags/v${version}`, {
+			const repository = process.env['VSCODE_SYSROOT_REPOSITORY'] ?? 'Microsoft/vscode-linux-build-agent';
+			const actualVersion = process.env['VSCODE_SYSROOT_VERSION'] ?? version;
+			const response = await fetch(`https://api.github.com/repos/${repository}/releases/tags/v${actualVersion}`, {
 				headers: ghApiHeaders,
@@ -92,3 +94,3 @@ async function fetchUrl(options: IFetchOptions, retries = 10, retryDelay = 1000)
 				if (!asset) {
-					throw new Error(`Could not find asset in release of Microsoft/vscode-linux-build-agent @ ${version}`);
+					throw new Error(`Could not find asset in release of ${repository} @ ${actualVersion}`);
 				}
