diff --git a/build/linux/dependencies-generator.js b/build/linux/dependencies-generator.js
index 7521729..92c0d8e 100644
--- a/build/linux/dependencies-generator.js
+++ b/build/linux/dependencies-generator.js
@@ -27,3 +27,3 @@ const product = require("../../product.json");
 // are valid, are in dep-lists.ts
-const FAIL_BUILD_FOR_NEW_DEPENDENCIES = true;
+const FAIL_BUILD_FOR_NEW_DEPENDENCIES = false;
 // Based on https://source.chromium.org/chromium/chromium/src/+/refs/tags/134.0.6998.205:chrome/installer/linux/BUILD.gn;l=64-80
@@ -60,3 +60,3 @@ async function getDependencies(packageType, buildDir, applicationName, arch) {
     // Add the tunnel binary.
-    files.push(path_1.default.join(buildDir, 'bin', product.tunnelApplicationName));
+    // files.push(path_1.default.join(buildDir, 'bin', product.tunnelApplicationName));
     // Add the main executable.
diff --git a/build/linux/dependencies-generator.ts b/build/linux/dependencies-generator.ts
index 9383703..5e6bcf2 100644
--- a/build/linux/dependencies-generator.ts
+++ b/build/linux/dependencies-generator.ts
@@ -25,3 +25,3 @@ import product = require('../../product.json');
 // are valid, are in dep-lists.ts
-const FAIL_BUILD_FOR_NEW_DEPENDENCIES: boolean = true;
+const FAIL_BUILD_FOR_NEW_DEPENDENCIES: boolean = false;
 
@@ -62,3 +62,3 @@ export async function getDependencies(packageType: 'deb' | 'rpm', buildDir: stri
 	// Add the tunnel binary.
-	files.push(path.join(buildDir, 'bin', product.tunnelApplicationName));
+	// files.push(path.join(buildDir, 'bin', product.tunnelApplicationName));
 	// Add the main executable.
