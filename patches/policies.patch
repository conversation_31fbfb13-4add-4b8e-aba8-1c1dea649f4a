diff --git a/build/.moduleignore b/build/.moduleignore
index 3e654cf..d9ee9f5 100644
--- a/build/.moduleignore
+++ b/build/.moduleignore
@@ -126,9 +126,11 @@ vsda/**
 
-@vscode/policy-watcher/build/**
-@vscode/policy-watcher/.husky/**
-@vscode/policy-watcher/src/**
-@vscode/policy-watcher/binding.gyp
-@vscode/policy-watcher/README.md
-@vscode/policy-watcher/index.d.ts
-!@vscode/policy-watcher/build/Release/vscode-policy-watcher.node
+@vscodium/policy-watcher/.github/**
+@vscodium/policy-watcher/.husky/**
+@vscodium/policy-watcher/build/**
+@vscodium/policy-watcher/src/**
+@vscodium/policy-watcher/.release-it.yml
+@vscodium/policy-watcher/binding.gyp
+@vscodium/policy-watcher/README.md
+@vscodium/policy-watcher/index.d.ts
+!@vscodium/policy-watcher/build/Release/vscodium-policy-watcher.node
 
diff --git a/build/lib/policies.js b/build/lib/policies.js
index ac69762..db1fd3d 100644
--- a/build/lib/policies.js
+++ b/build/lib/policies.js
@@ -81,3 +81,3 @@ class BasePolicy {
         return [
-            `<policy name="${this.name}" class="Both" displayName="$(string.${this.name})" explainText="$(string.${this.name}_${this.description.nlsKey.replace(/\./g, '_')})" key="Software\\Policies\\Microsoft\\${regKey}" presentation="$(presentation.${this.name})">`,
+            `<policy name="${this.name}" class="Both" displayName="$(string.${this.name})" explainText="$(string.${this.name}_${this.description.nlsKey.replace(/\./g, '_')})" key="Software\\Policies\\!!ORG_NAME!!\\${regKey}" presentation="$(presentation.${this.name})">`,
             `	<parentCategory ref="${this.category.name.nlsKey}" />`,
@@ -501,3 +501,3 @@ function renderADMX(regKey, versions, categories, policies) {
 	<policyNamespaces>
-		<target prefix="${regKey}" namespace="Microsoft.Policies.${regKey}" />
+		<target prefix="${regKey}" namespace="!!ORG_NAME!!.Policies.${regKey}" />
 	</policyNamespaces>
diff --git a/build/lib/policies.ts b/build/lib/policies.ts
index 34d20e9..8404cdf 100644
--- a/build/lib/policies.ts
+++ b/build/lib/policies.ts
@@ -107,3 +107,3 @@ abstract class BasePolicy implements Policy {
 		return [
-			`<policy name="${this.name}" class="Both" displayName="$(string.${this.name})" explainText="$(string.${this.name}_${this.description.nlsKey.replace(/\./g, '_')})" key="Software\\Policies\\Microsoft\\${regKey}" presentation="$(presentation.${this.name})">`,
+			`<policy name="${this.name}" class="Both" displayName="$(string.${this.name})" explainText="$(string.${this.name}_${this.description.nlsKey.replace(/\./g, '_')})" key="Software\\Policies\\!!ORG_NAME!!\\${regKey}" presentation="$(presentation.${this.name})">`,
 			`	<parentCategory ref="${this.category.name.nlsKey}" />`,
@@ -703,3 +703,3 @@ function renderADMX(regKey: string, versions: string[], categories: Category[],
 	<policyNamespaces>
-		<target prefix="${regKey}" namespace="Microsoft.Policies.${regKey}" />
+		<target prefix="${regKey}" namespace="!!ORG_NAME!!.Policies.${regKey}" />
 	</policyNamespaces>
diff --git a/eslint.config.js b/eslint.config.js
index 47aebd4..6c86ef9 100644
--- a/eslint.config.js
+++ b/eslint.config.js
@@ -801,3 +801,3 @@ export default tseslint.config(
 						'@vscode/iconv-lite-umd',
-						'@vscode/policy-watcher',
+						'@vscodium/policy-watcher',
 						'@vscode/proxy-agent',
diff --git a/package-lock.json b/package-lock.json
index d0100d4..2529cad 100644
--- a/package-lock.json
+++ b/package-lock.json
@@ -18,3 +18,2 @@
         "@vscode/iconv-lite-umd": "0.7.0",
-        "@vscode/policy-watcher": "^1.3.2",
         "@vscode/proxy-agent": "^0.32.0",
@@ -29,2 +28,3 @@
         "@vscode/windows-registry": "^1.1.0",
+        "@vscodium/policy-watcher": "^1.3.2-252465",
         "@xterm/addon-clipboard": "^0.2.0-beta.90",
@@ -2701,22 +2701,2 @@
     },
-    "node_modules/@vscode/policy-watcher": {
-      "version": "1.3.2",
-      "resolved": "https://registry.npmjs.org/@vscode/policy-watcher/-/policy-watcher-1.3.2.tgz",
-      "integrity": "sha512-fmNPYysU2ioH99uCaBPiRblEZSnir5cTmc7w91hAxAoYoGpHt2PZPxT5eIOn7FGmPOsjLdQcd6fduFJGYVD4Mw==",
-      "hasInstallScript": true,
-      "license": "MIT",
-      "dependencies": {
-        "bindings": "^1.5.0",
-        "node-addon-api": "^8.2.0"
-      }
-    },
-    "node_modules/@vscode/policy-watcher/node_modules/node-addon-api": {
-      "version": "8.2.0",
-      "resolved": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-8.2.0.tgz",
-      "integrity": "sha512-qnyuI2ROiCkye42n9Tj5aX1ns7rzj6n7zW1XReSnLSL9v/vbLeR6fJq6PU27YU/ICfYw6W7Ouk/N7cysWu/hlw==",
-      "license": "MIT",
-      "engines": {
-        "node": "^18 || ^20 || >= 21"
-      }
-    },
     "node_modules/@vscode/proxy-agent": {
@@ -3117,2 +3097,22 @@
     },
+    "node_modules/@vscodium/policy-watcher": {
+      "version": "1.3.2-252465",
+      "resolved": "https://registry.npmjs.org/@vscodium/policy-watcher/-/policy-watcher-1.3.2-252465.tgz",
+      "integrity": "sha512-kpnb656HMteBIm8d9LhBpQ5gL2A/4rJrsaLCF0D8IWyrZAQ0UR9EzXM6tZ6p5H+KWot3QUjm0Gry6vMV1yye5Q==",
+      "hasInstallScript": true,
+      "license": "MIT",
+      "dependencies": {
+        "bindings": "^1.5.0",
+        "node-addon-api": "^8.2.0"
+      }
+    },
+    "node_modules/@vscodium/policy-watcher/node_modules/node-addon-api": {
+      "version": "8.3.0",
+      "resolved": "https://registry.npmjs.org/node-addon-api/-/node-addon-api-8.3.0.tgz",
+      "integrity": "sha512-8VOpLHFrOQlAH+qA0ZzuGRlALRA6/LVh8QJldbrC4DY0hXoMP0l4Acq8TzFC018HztWiRqyCEj2aTWY2UvnJUg==",
+      "license": "MIT",
+      "engines": {
+        "node": "^18 || ^20 || >= 21"
+      }
+    },
     "node_modules/@webassemblyjs/ast": {
diff --git a/package.json b/package.json
index 4be2f9c..657d262 100644
--- a/package.json
+++ b/package.json
@@ -77,3 +77,3 @@
     "@vscode/iconv-lite-umd": "0.7.0",
-    "@vscode/policy-watcher": "^1.3.2",
+    "@vscodium/policy-watcher": "^1.3.2-252465",
     "@vscode/proxy-agent": "^0.32.0",
diff --git a/src/vs/base/test/node/uri.perf.data.txt b/src/vs/base/test/node/uri.perf.data.txt
index ee0a24b..881ce36 100644
--- a/src/vs/base/test/node/uri.perf.data.txt
+++ b/src/vs/base/test/node/uri.perf.data.txt
@@ -14698,48 +14698,48 @@
 /Users/<USER>/node_modules/@vscode/node-addon-api/node_addon_api.Makefile
-/Users/<USER>/node_modules/@vscode/policy-watcher/LICENSE
-/Users/<USER>/node_modules/@vscode/policy-watcher/binding.gyp
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/LICENSE.md
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/tools/conversion.js
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/tools/eslint-format.js
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/tools/clang-format.js
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/tools/README.md
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/tools/check-napi.js
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/except.gypi
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/napi-inl.deprecated.h
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/index.js
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/README.md
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/napi-inl.h
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/node_api.gyp
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/napi.h
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/package.json
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/noexcept.gypi
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/common.gypi
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/nothing.c
-/Users/<USER>/node_modules/@vscode/policy-watcher/node_modules/node-addon-api/package-support.json
-/Users/<USER>/node_modules/@vscode/policy-watcher/index.js
-/Users/<USER>/node_modules/@vscode/policy-watcher/README.md
-/Users/<USER>/node_modules/@vscode/policy-watcher/package.json
-/Users/<USER>/node_modules/@vscode/policy-watcher/build/vscode-policy-watcher.target.mk
-/Users/<USER>/node_modules/@vscode/policy-watcher/build/gyp-mac-tool
-/Users/<USER>/node_modules/@vscode/policy-watcher/build/Makefile
-/Users/<USER>/node_modules/@vscode/policy-watcher/build/Release/obj.target/vscode-policy-watcher/src/main.o
-/Users/<USER>/node_modules/@vscode/policy-watcher/build/Release/obj.target/vscode-policy-watcher/src/macos/PolicyWatcher.o
-/Users/<USER>/node_modules/@vscode/policy-watcher/build/Release/vscode-policy-watcher.node
-/Users/<USER>/node_modules/@vscode/policy-watcher/build/Release/.deps/Release/vscode-policy-watcher.node.d
-/Users/<USER>/node_modules/@vscode/policy-watcher/build/Release/.deps/Release/obj.target/vscode-policy-watcher/src/macos/PolicyWatcher.o.d
-/Users/<USER>/node_modules/@vscode/policy-watcher/build/Release/.deps/Release/obj.target/vscode-policy-watcher/src/main.o.d
-/Users/<USER>/node_modules/@vscode/policy-watcher/build/config.gypi
-/Users/<USER>/node_modules/@vscode/policy-watcher/build/binding.Makefile
-/Users/<USER>/node_modules/@vscode/policy-watcher/index.d.ts
-/Users/<USER>/node_modules/@vscode/policy-watcher/src/main.cc
-/Users/<USER>/node_modules/@vscode/policy-watcher/src/macos/PolicyWatcher.cc
-/Users/<USER>/node_modules/@vscode/policy-watcher/src/PolicyWatcher.hh
-/Users/<USER>/node_modules/@vscode/policy-watcher/src/linux/PolicyWatcher.cc
-/Users/<USER>/node_modules/@vscode/policy-watcher/src/Policy.hh
-/Users/<USER>/node_modules/@vscode/policy-watcher/src/windows/RegistryPolicy.hh
-/Users/<USER>/node_modules/@vscode/policy-watcher/src/windows/PolicyWatcher.cc
-/Users/<USER>/node_modules/@vscode/policy-watcher/src/windows/NumberPolicy.cc
-/Users/<USER>/node_modules/@vscode/policy-watcher/src/windows/StringPolicy.hh
-/Users/<USER>/node_modules/@vscode/policy-watcher/src/windows/StringPolicy.cc
-/Users/<USER>/node_modules/@vscode/policy-watcher/src/windows/NumberPolicy.hh
+/Users/<USER>/node_modules/@vscodium/policy-watcher/LICENSE
+/Users/<USER>/node_modules/@vscodium/policy-watcher/binding.gyp
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/LICENSE.md
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/tools/conversion.js
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/tools/eslint-format.js
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/tools/clang-format.js
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/tools/README.md
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/tools/check-napi.js
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/except.gypi
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/napi-inl.deprecated.h
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/index.js
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/README.md
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/napi-inl.h
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/node_api.gyp
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/napi.h
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/package.json
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/noexcept.gypi
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/common.gypi
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/nothing.c
+/Users/<USER>/node_modules/@vscodium/policy-watcher/node_modules/node-addon-api/package-support.json
+/Users/<USER>/node_modules/@vscodium/policy-watcher/index.js
+/Users/<USER>/node_modules/@vscodium/policy-watcher/README.md
+/Users/<USER>/node_modules/@vscodium/policy-watcher/package.json
+/Users/<USER>/node_modules/@vscodium/policy-watcher/build/vscode-policy-watcher.target.mk
+/Users/<USER>/node_modules/@vscodium/policy-watcher/build/gyp-mac-tool
+/Users/<USER>/node_modules/@vscodium/policy-watcher/build/Makefile
+/Users/<USER>/node_modules/@vscodium/policy-watcher/build/Release/obj.target/vscode-policy-watcher/src/main.o
+/Users/<USER>/node_modules/@vscodium/policy-watcher/build/Release/obj.target/vscode-policy-watcher/src/macos/PolicyWatcher.o
+/Users/<USER>/node_modules/@vscodium/policy-watcher/build/Release/vscode-policy-watcher.node
+/Users/<USER>/node_modules/@vscodium/policy-watcher/build/Release/.deps/Release/vscode-policy-watcher.node.d
+/Users/<USER>/node_modules/@vscodium/policy-watcher/build/Release/.deps/Release/obj.target/vscode-policy-watcher/src/macos/PolicyWatcher.o.d
+/Users/<USER>/node_modules/@vscodium/policy-watcher/build/Release/.deps/Release/obj.target/vscode-policy-watcher/src/main.o.d
+/Users/<USER>/node_modules/@vscodium/policy-watcher/build/config.gypi
+/Users/<USER>/node_modules/@vscodium/policy-watcher/build/binding.Makefile
+/Users/<USER>/node_modules/@vscodium/policy-watcher/index.d.ts
+/Users/<USER>/node_modules/@vscodium/policy-watcher/src/main.cc
+/Users/<USER>/node_modules/@vscodium/policy-watcher/src/macos/PolicyWatcher.cc
+/Users/<USER>/node_modules/@vscodium/policy-watcher/src/PolicyWatcher.hh
+/Users/<USER>/node_modules/@vscodium/policy-watcher/src/linux/PolicyWatcher.cc
+/Users/<USER>/node_modules/@vscodium/policy-watcher/src/Policy.hh
+/Users/<USER>/node_modules/@vscodium/policy-watcher/src/windows/RegistryPolicy.hh
+/Users/<USER>/node_modules/@vscodium/policy-watcher/src/windows/PolicyWatcher.cc
+/Users/<USER>/node_modules/@vscodium/policy-watcher/src/windows/NumberPolicy.cc
+/Users/<USER>/node_modules/@vscodium/policy-watcher/src/windows/StringPolicy.hh
+/Users/<USER>/node_modules/@vscodium/policy-watcher/src/windows/StringPolicy.cc
+/Users/<USER>/node_modules/@vscodium/policy-watcher/src/windows/NumberPolicy.hh
 /Users/<USER>/node_modules/@vscode/vscode-languagedetection/CODE_OF_CONDUCT.md
diff --git a/src/vs/platform/environment/test/node/nativeModules.integrationTest.ts b/src/vs/platform/environment/test/node/nativeModules.integrationTest.ts
index e0b99f3..f3af586 100644
--- a/src/vs/platform/environment/test/node/nativeModules.integrationTest.ts
+++ b/src/vs/platform/environment/test/node/nativeModules.integrationTest.ts
@@ -62,5 +62,5 @@ flakySuite('Native Modules (all platforms)', () => {
 
-	test('@vscode/policy-watcher', async () => {
-		const watcher = await import('@vscode/policy-watcher');
-		assert.ok(typeof watcher.createWatcher === 'function', testErrorMessage('@vscode/policy-watcher'));
+	test('@vscodium/policy-watcher', async () => {
+		const watcher = await import('@vscodium/policy-watcher');
+		assert.ok(typeof watcher.createWatcher === 'function', testErrorMessage('@vscodium/policy-watcher'));
 	});
diff --git a/src/vs/platform/policy/node/nativePolicyService.ts b/src/vs/platform/policy/node/nativePolicyService.ts
index 67f7892..840d655 100644
--- a/src/vs/platform/policy/node/nativePolicyService.ts
+++ b/src/vs/platform/policy/node/nativePolicyService.ts
@@ -8,3 +8,3 @@ import { IStringDictionary } from '../../../base/common/collections.js';
 import { Throttler } from '../../../base/common/async.js';
-import type { PolicyUpdate, Watcher } from '@vscode/policy-watcher';
+import type { PolicyUpdate, Watcher } from '@vscodium/policy-watcher';
 import { MutableDisposable } from '../../../base/common/lifecycle.js';
@@ -27,3 +27,3 @@ export class NativePolicyService extends AbstractPolicyService implements IPolic
 
-		const { createWatcher } = await import('@vscode/policy-watcher');
+		const { createWatcher } = await import('@vscodium/policy-watcher');
 
@@ -31,3 +31,3 @@ export class NativePolicyService extends AbstractPolicyService implements IPolic
 			try {
-				this.watcher.value = createWatcher(this.productName, policyDefinitions, update => {
+				this.watcher.value = createWatcher('!!ORG_NAME!!', this.productName, policyDefinitions, update => {
 					this._onDidPolicyChange(update);
