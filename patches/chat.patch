diff --git a/src/vs/workbench/contrib/chat/browser/chat.contribution.ts b/src/vs/workbench/contrib/chat/browser/chat.contribution.ts
index 56220de..68d708f 100644
--- a/src/vs/workbench/contrib/chat/browser/chat.contribution.ts
+++ b/src/vs/workbench/contrib/chat/browser/chat.contribution.ts
@@ -142,3 +142,3 @@ configurationRegistry.registerConfiguration({
 			markdownDescription: nls.localize('chat.commandCenter.enabled', "Controls whether the command center shows a menu for actions to control Copilot (requires {0}).", '`#window.commandCenter#`'),
-			default: true
+			default: false
 		},
