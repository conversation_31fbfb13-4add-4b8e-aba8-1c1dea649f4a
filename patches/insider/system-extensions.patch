diff --git a/src/vs/workbench/contrib/extensions/browser/extensionsWorkbenchService.ts b/src/vs/workbench/contrib/extensions/browser/extensionsWorkbenchService.ts
index 150908a..0759a8d 100644
--- a/src/vs/workbench/contrib/extensions/browser/extensionsWorkbenchService.ts
+++ b/src/vs/workbench/contrib/extensions/browser/extensionsWorkbenchService.ts
@@ -104,2 +104,3 @@ export class Extension implements IExtension {
 		@IFileService private readonly fileService: IFileService,
+		// @ts-ignore
 		@IProductService private readonly productService: IProductService
@@ -325,3 +326,3 @@ export class Extension implements IExtension {
 			// Do not allow updating system extensions in stable
-			if (this.type === ExtensionType.System && this.productService.quality === 'stable') {
+			if (this.type === ExtensionType.System) {
 				return false;
