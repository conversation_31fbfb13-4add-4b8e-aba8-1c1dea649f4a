diff --git a/build/gulpfile.vscode.win32.js b/build/gulpfile.vscode.win32.js
index 5adfdfb..dfdb2af 100644
--- a/build/gulpfile.vscode.win32.js
+++ b/build/gulpfile.vscode.win32.js
@@ -115,6 +115,6 @@ function buildWin32Setup(arch, target) {
 
-		if (quality === 'insider') {
-			definitions['AppxPackage'] = `code_insiders_explorer_${arch}.appx`;
-			definitions['AppxPackageFullname'] = `Microsoft.${product.win32RegValueName}_1.0.0.0_neutral__8wekyb3d8bbwe`;
-		}
+		// if (quality === 'insider') {
+		// 	definitions['AppxPackage'] = `code_insiders_explorer_${arch}.appx`;
+		// 	definitions['AppxPackageFullname'] = `Microsoft.${product.win32RegValueName}_1.0.0.0_neutral__8wekyb3d8bbwe`;
+		// }
 
