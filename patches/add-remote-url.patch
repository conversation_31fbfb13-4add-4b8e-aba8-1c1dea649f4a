diff --git a/build/gulpfile.reh.js b/build/gulpfile.reh.js
index c1d64c0..3e60e80 100644
--- a/build/gulpfile.reh.js
+++ b/build/gulpfile.reh.js
@@ -321,3 +321,3 @@ function packageTask(type, platform, arch, sourceFolderName, destinationFolderNa
 		const productJsonStream = gulp.src(['product.json'], { base: '.' })
-			.pipe(json({ commit, date: readISODate('out-build'), version }))
+			.pipe(json({ commit, date: readISODate('out-build'), version, serverDownloadUrlTemplate: 'https://github.com/!!ASSETS_REPOSITORY!!/releases/download/!!RELEASE_VERSION!!/!!APP_NAME_LC!!-reh-${os}-${arch}-!!RELEASE_VERSION!!.tar.gz' }))
 			.pipe(es.through(function (file) {
diff --git a/build/gulpfile.vscode.js b/build/gulpfile.vscode.js
index 7046ee0..9aa7bb0 100644
--- a/build/gulpfile.vscode.js
+++ b/build/gulpfile.vscode.js
@@ -288,3 +288,3 @@ function packageTask(platform, arch, sourceFolderName, destinationFolderName, op
 		const productJsonStream = gulp.src(['product.json'], { base: '.' })
-			.pipe(json({ commit, date: readISODate('out-build'), checksums, version }))
+			.pipe(json({ commit, date: readISODate('out-build'), checksums, version, serverDownloadUrlTemplate: 'https://github.com/!!ASSETS_REPOSITORY!!/releases/download/!!RELEASE_VERSION!!/!!APP_NAME_LC!!-reh-${os}-${arch}-!!RELEASE_VERSION!!.tar.gz' }))
 			.pipe(es.through(function (file) {
