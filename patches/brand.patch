diff --git a/extensions/configuration-editing/src/configurationEditingMain.ts b/extensions/configuration-editing/src/configurationEditingMain.ts
index 2578270..99c8ca5 100644
--- a/extensions/configuration-editing/src/configurationEditingMain.ts
+++ b/extensions/configuration-editing/src/configurationEditingMain.ts
@@ -54,4 +54,4 @@ function registerVariableCompletions(pattern: string): vscode.Disposable {
 				return [
-					{ label: 'workspaceFolder', detail: vscode.l10n.t("The path of the folder opened in VS Code") },
-					{ label: 'workspaceFolderBasename', detail: vscode.l10n.t("The name of the folder opened in VS Code without any slashes (/)") },
+					{ label: 'workspaceFolder', detail: vscode.l10n.t("The path of the folder opened in !!APP_NAME!!") },
+					{ label: 'workspaceFolderBasename', detail: vscode.l10n.t("The name of the folder opened in !!APP_NAME!! without any slashes (/)") },
 					{ label: 'fileWorkspaceFolderBasename', detail: vscode.l10n.t("The current opened file workspace folder name without any slashes (/)") },
diff --git a/extensions/configuration-editing/src/settingsDocumentHelper.ts b/extensions/configuration-editing/src/settingsDocumentHelper.ts
index 12b50f3..7cb0d1b 100644
--- a/extensions/configuration-editing/src/settingsDocumentHelper.ts
+++ b/extensions/configuration-editing/src/settingsDocumentHelper.ts
@@ -123,3 +123,3 @@ export class SettingsDocument {
 		completions.push(this.newSimpleCompletionItem(getText('folderPath'), range, vscode.l10n.t("file path of the workspace folder the file is contained in (e.g. /Users/<USER>/myFolder)")));
-		completions.push(this.newSimpleCompletionItem(getText('appName'), range, vscode.l10n.t("e.g. VS Code")));
+		completions.push(this.newSimpleCompletionItem(getText('appName'), range, vscode.l10n.t("e.g. !!APP_NAME!!")));
 		completions.push(this.newSimpleCompletionItem(getText('remoteName'), range, vscode.l10n.t("e.g. SSH")));
diff --git a/extensions/css-language-features/package.nls.json b/extensions/css-language-features/package.nls.json
index 057ec21..53c32b4 100644
--- a/extensions/css-language-features/package.nls.json
+++ b/extensions/css-language-features/package.nls.json
@@ -4,4 +4,4 @@
 	"css.title": "CSS",
-	"css.customData.desc": "A list of relative file paths pointing to JSON files following the [custom data format](https://github.com/microsoft/vscode-css-languageservice/blob/master/docs/customData.md).\n\nVS Code loads custom data on startup to enhance its CSS support for CSS custom properties (variables), at-rules, pseudo-classes, and pseudo-elements you specify in the JSON files.\n\nThe file paths are relative to workspace and only workspace folder settings are considered.",
-	"css.completion.triggerPropertyValueCompletion.desc": "By default, VS Code triggers property value completion after selecting a CSS property. Use this setting to disable this behavior.",
+	"css.customData.desc": "A list of relative file paths pointing to JSON files following the [custom data format](https://github.com/microsoft/vscode-css-languageservice/blob/master/docs/customData.md).\n\n!!APP_NAME!! loads custom data on startup to enhance its CSS support for CSS custom properties (variables), at-rules, pseudo-classes, and pseudo-elements you specify in the JSON files.\n\nThe file paths are relative to workspace and only workspace folder settings are considered.",
+	"css.completion.triggerPropertyValueCompletion.desc": "By default, !!APP_NAME!! triggers property value completion after selecting a CSS property. Use this setting to disable this behavior.",
 	"css.completion.completePropertyWithSemicolon.desc": "Insert semicolon at end of line when completing CSS properties.",
@@ -27,3 +27,3 @@
 	"css.lint.zeroUnits.desc": "No unit for zero needed.",
-	"css.trace.server.desc": "Traces the communication between VS Code and the CSS language server.",
+	"css.trace.server.desc": "Traces the communication between !!APP_NAME!! and the CSS language server.",
 	"css.validate.title": "Controls CSS validation and problem severities.",
@@ -40,3 +40,3 @@
 	"less.title": "LESS",
-	"less.completion.triggerPropertyValueCompletion.desc": "By default, VS Code triggers property value completion after selecting a CSS property. Use this setting to disable this behavior.",
+	"less.completion.triggerPropertyValueCompletion.desc": "By default, !!APP_NAME!! triggers property value completion after selecting a CSS property. Use this setting to disable this behavior.",
 	"less.completion.completePropertyWithSemicolon.desc": "Insert semicolon at end of line when completing CSS properties.",
@@ -74,3 +74,3 @@
 	"scss.title": "SCSS (Sass)",
-	"scss.completion.triggerPropertyValueCompletion.desc": "By default, VS Code triggers property value completion after selecting a CSS property. Use this setting to disable this behavior.",
+	"scss.completion.triggerPropertyValueCompletion.desc": "By default, !!APP_NAME!! triggers property value completion after selecting a CSS property. Use this setting to disable this behavior.",
 	"scss.completion.completePropertyWithSemicolon.desc": "Insert semicolon at end of line when completing CSS properties.",
diff --git a/extensions/emmet/package.nls.json b/extensions/emmet/package.nls.json
index 2a58c39..8224615 100644
--- a/extensions/emmet/package.nls.json
+++ b/extensions/emmet/package.nls.json
@@ -1,3 +1,3 @@
 {
-	"description": "Emmet support for VS Code",
+	"description": "Emmet support for !!APP_NAME!!",
 	"command.wrapWithAbbreviation": "Wrap with Abbreviation",
diff --git a/extensions/extension-editing/src/constants.ts b/extensions/extension-editing/src/constants.ts
index 1be4d0e..647b147 100644
--- a/extensions/extension-editing/src/constants.ts
+++ b/extensions/extension-editing/src/constants.ts
@@ -8,2 +8,2 @@ import { l10n } from 'vscode';
 export const implicitActivationEvent = l10n.t("This activation event cannot be explicitly listed by your extension.");
-export const redundantImplicitActivationEvent = l10n.t("This activation event can be removed as VS Code generates these automatically from your package.json contribution declarations.");
+export const redundantImplicitActivationEvent = l10n.t("This activation event can be removed as !!APP_NAME!! generates these automatically from your package.json contribution declarations.");
diff --git a/extensions/extension-editing/src/extensionLinter.ts b/extensions/extension-editing/src/extensionLinter.ts
index be7eea1..0737bab 100644
--- a/extensions/extension-editing/src/extensionLinter.ts
+++ b/extensions/extension-editing/src/extensionLinter.ts
@@ -34,4 +34,4 @@ const relativeUrlRequiresHttpsRepository = l10n.t("Relative image URLs require a
 const relativeBadgeUrlRequiresHttpsRepository = l10n.t("Relative badge URLs require a repository with HTTPS protocol to be specified in this package.json.");
-const apiProposalNotListed = l10n.t("This proposal cannot be used because for this extension the product defines a fixed set of API proposals. You can test your extension but before publishing you MUST reach out to the VS Code team.");
-const bumpEngineForImplicitActivationEvents = l10n.t("This activation event can be removed for extensions targeting engine version ^1.75 as VS Code will generate these automatically from your package.json contribution declarations.");
+const apiProposalNotListed = l10n.t("This proposal cannot be used because for this extension the product defines a fixed set of API proposals. You can test your extension but before publishing you MUST reach out to the !!APP_NAME!! team.");
+const bumpEngineForImplicitActivationEvents = l10n.t("This activation event can be removed for extensions targeting engine version ^1.75 as !!APP_NAME!! will generate these automatically from your package.json contribution declarations.");
 const starActivation = l10n.t("Using '*' activation is usually a bad idea as it impacts performance.");
diff --git a/extensions/git/package.nls.json b/extensions/git/package.nls.json
index 403f704..2f838cd 100644
--- a/extensions/git/package.nls.json
+++ b/extensions/git/package.nls.json
@@ -228,3 +228,3 @@
 			"{Locked='](command:git.showOutput'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -256,4 +256,4 @@
 	"config.showCommitInput": "Controls whether to show the commit input in the Git source control panel.",
-	"config.terminalAuthentication": "Controls whether to enable VS Code to be the authentication handler for Git processes spawned in the Integrated Terminal. Note: Terminals need to be restarted to pick up a change in this setting.",
-	"config.terminalGitEditor": "Controls whether to enable VS Code to be the Git editor for Git processes spawned in the integrated terminal. Note: Terminals need to be restarted to pick up a change in this setting.",
+	"config.terminalAuthentication": "Controls whether to enable !!APP_NAME!! to be the authentication handler for Git processes spawned in the Integrated Terminal. Note: Terminals need to be restarted to pick up a change in this setting.",
+	"config.terminalGitEditor": "Controls whether to enable !!APP_NAME!! to be the Git editor for Git processes spawned in the integrated terminal. Note: Terminals need to be restarted to pick up a change in this setting.",
 	"config.timeline.showAuthor": "Controls whether to show the commit author in the Timeline view.",
@@ -324,3 +324,3 @@
 			"{Locked='](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -334,3 +334,3 @@
 			"{Locked='](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -344,3 +344,3 @@
 			"{Locked='](command:workbench.extensions.search?%22%40category%3A%5C%22scm%20providers%5C%22%22'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -356,6 +356,6 @@
 	"view.workbench.scm.disabled": {
-		"message": "If you would like to use Git features, please enable Git in your [settings](command:workbench.action.openSettings?%5B%22git.enabled%22%5D).\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).",
+		"message": "If you would like to use Git features, please enable Git in your [settings](command:workbench.action.openSettings?%5B%22git.enabled%22%5D).\nTo learn more about how to use Git and source control in !!APP_NAME!! [read our docs](https://aka.ms/vscode-scm).",
 		"comment": [
 			"{Locked='](command:workbench.action.openSettings?%5B%22git.enabled%22%5D'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -364,6 +364,6 @@
 	"view.workbench.scm.empty": {
-		"message": "In order to use Git features, you can open a folder containing a Git repository or clone from a URL.\n[Open Folder](command:vscode.openFolder)\n[Clone Repository](command:git.cloneRecursive)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).",
+		"message": "In order to use Git features, you can open a folder containing a Git repository or clone from a URL.\n[Open Folder](command:vscode.openFolder)\n[Clone Repository](command:git.cloneRecursive)\nTo learn more about how to use Git and source control in !!APP_NAME!! [read our docs](https://aka.ms/vscode-scm).",
 		"comment": [
 			"{Locked='](command:vscode.openFolder'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -372,6 +372,6 @@
 	"view.workbench.scm.folder": {
-		"message": "The folder currently open doesn't have a Git repository. You can initialize a repository which will enable source control features powered by Git.\n[Initialize Repository](command:git.init?%5Btrue%5D)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).",
+		"message": "The folder currently open doesn't have a Git repository. You can initialize a repository which will enable source control features powered by Git.\n[Initialize Repository](command:git.init?%5Btrue%5D)\nTo learn more about how to use Git and source control in !!APP_NAME!! [read our docs](https://aka.ms/vscode-scm).",
 		"comment": [
 			"{Locked='](command:git.init?%5Btrue%5D'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -380,6 +380,6 @@
 	"view.workbench.scm.workspace": {
-		"message": "The workspace currently open doesn't have any folders containing Git repositories. You can initialize a repository on a folder which will enable source control features powered by Git.\n[Initialize Repository](command:git.init)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).",
+		"message": "The workspace currently open doesn't have any folders containing Git repositories. You can initialize a repository on a folder which will enable source control features powered by Git.\n[Initialize Repository](command:git.init)\nTo learn more about how to use Git and source control in !!APP_NAME!! [read our docs](https://aka.ms/vscode-scm).",
 		"comment": [
 			"{Locked='](command:git.init'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -388,6 +388,6 @@
 	"view.workbench.scm.emptyWorkspace": {
-		"message": "The workspace currently open doesn't have any folders containing Git repositories.\n[Add Folder to Workspace](command:workbench.action.addRootFolder)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).",
+		"message": "The workspace currently open doesn't have any folders containing Git repositories.\n[Add Folder to Workspace](command:workbench.action.addRootFolder)\nTo learn more about how to use Git and source control in !!APP_NAME!! [read our docs](https://aka.ms/vscode-scm).",
 		"comment": [
 			"{Locked='](command:workbench.action.addRootFolder'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -406,3 +406,3 @@
 			"{Locked='](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -415,3 +415,3 @@
 			"{Locked='](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -423,3 +423,3 @@
 			"{Locked='](command:git.manageUnsafeRepositories'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -431,3 +431,3 @@
 			"{Locked='](command:git.manageUnsafeRepositories'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -436,6 +436,6 @@
 	"view.workbench.scm.closedRepository": {
-		"message": "A Git repository was found that was previously closed.\n[Reopen Closed Repository](command:git.reopenClosedRepositories)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).",
+		"message": "A Git repository was found that was previously closed.\n[Reopen Closed Repository](command:git.reopenClosedRepositories)\nTo learn more about how to use Git and source control in !!APP_NAME!! [read our docs](https://aka.ms/vscode-scm).",
 		"comment": [
 			"{Locked='](command:git.reopenClosedRepositories'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -444,6 +444,6 @@
 	"view.workbench.scm.closedRepositories": {
-		"message": "Git repositories were found that were previously closed.\n[Reopen Closed Repositories](command:git.reopenClosedRepositories)\nTo learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm).",
+		"message": "Git repositories were found that were previously closed.\n[Reopen Closed Repositories](command:git.reopenClosedRepositories)\nTo learn more about how to use Git and source control in !!APP_NAME!! [read our docs](https://aka.ms/vscode-scm).",
 		"comment": [
 			"{Locked='](command:git.reopenClosedRepositories'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -455,3 +455,3 @@
 			"{Locked='](command:git.clone'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -459,3 +459,3 @@
 	},
-	"view.workbench.learnMore": "To learn more about how to use Git and source control in VS Code [read our docs](https://aka.ms/vscode-scm)."
+	"view.workbench.learnMore": "To learn more about how to use Git and source control in !!APP_NAME!! [read our docs](https://aka.ms/vscode-scm)."
 }
diff --git a/extensions/github/package.nls.json b/extensions/github/package.nls.json
index 40271be..00dae16 100644
--- a/extensions/github/package.nls.json
+++ b/extensions/github/package.nls.json
@@ -2,3 +2,3 @@
 	"displayName": "GitHub",
-	"description": "GitHub features for VS Code",
+	"description": "GitHub features for !!APP_NAME!!",
 	"command.copyVscodeDevLink": "Copy vscode.dev Link",
@@ -8,3 +8,3 @@
 	"config.branchProtection": "Controls whether to query repository rules for GitHub repositories",
-	"config.gitAuthentication": "Controls whether to enable automatic GitHub authentication for git commands within VS Code.",
+	"config.gitAuthentication": "Controls whether to enable automatic GitHub authentication for git commands within !!APP_NAME!!.",
 	"config.gitProtocol": "Controls which protocol is used to clone a GitHub repository",
@@ -17,3 +17,3 @@
 			"{Locked='](command:github.publish'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
@@ -27,3 +27,3 @@
 			"{Locked='](command:github.publish'}",
-			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code",
+			"Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for !!APP_NAME!!",
 			"Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"
diff --git a/extensions/grunt/package.nls.json b/extensions/grunt/package.nls.json
index 789a579..12e230e 100644
--- a/extensions/grunt/package.nls.json
+++ b/extensions/grunt/package.nls.json
@@ -1,4 +1,4 @@
 {
-	"description": "Extension to add Grunt capabilities to VS Code.",
-	"displayName": "Grunt support for VS Code",
+	"description": "Extension to add Grunt capabilities to !!APP_NAME!!.",
+	"displayName": "Grunt support for !!APP_NAME!!",
 	"config.grunt.autoDetect": "Controls enablement of Grunt task detection. Grunt task detection can cause files in any open workspace to be executed.",
diff --git a/extensions/html-language-features/client/src/htmlClient.ts b/extensions/html-language-features/client/src/htmlClient.ts
index 2b0f961..a316a44 100644
--- a/extensions/html-language-features/client/src/htmlClient.ts
+++ b/extensions/html-language-features/client/src/htmlClient.ts
@@ -109,3 +109,3 @@ export async function startClient(context: ExtensionContext, newLanguageClient:
 					const configure = l10n.t('Configure');
-					const res = await window.showInformationMessage(l10n.t('VS Code now has built-in support for auto-renaming tags. Do you want to enable it?'), configure);
+					const res = await window.showInformationMessage(l10n.t('!!APP_NAME!! now has built-in support for auto-renaming tags. Do you want to enable it?'), configure);
 					if (res === configure) {
diff --git a/extensions/html-language-features/package.nls.json b/extensions/html-language-features/package.nls.json
index f36ecf3..0d795cb 100644
--- a/extensions/html-language-features/package.nls.json
+++ b/extensions/html-language-features/package.nls.json
@@ -3,3 +3,3 @@
 	"description": "Provides rich language support for HTML and Handlebar files",
-	"html.customData.desc": "A list of relative file paths pointing to JSON files following the [custom data format](https://github.com/microsoft/vscode-html-languageservice/blob/master/docs/customData.md).\n\nVS Code loads custom data on startup to enhance its HTML support for the custom HTML tags, attributes and attribute values you specify in the JSON files.\n\nThe file paths are relative to workspace and only workspace folder settings are considered.",
+	"html.customData.desc": "A list of relative file paths pointing to JSON files following the [custom data format](https://github.com/microsoft/vscode-html-languageservice/blob/master/docs/customData.md).\n\n!!APP_NAME!! loads custom data on startup to enhance its HTML support for the custom HTML tags, attributes and attribute values you specify in the JSON files.\n\nThe file paths are relative to workspace and only workspace folder settings are considered.",
 	"html.format.enable.desc": "Enable/disable default HTML formatter.",
@@ -25,3 +25,3 @@
 	"html.suggest.html5.desc": "Controls whether the built-in HTML language support suggests HTML5 tags, properties and values.",
-	"html.trace.server.desc": "Traces the communication between VS Code and the HTML language server.",
+	"html.trace.server.desc": "Traces the communication between !!APP_NAME!! and the HTML language server.",
 	"html.validate.scripts": "Controls whether the built-in HTML language support validates embedded scripts.",
diff --git a/extensions/html-language-features/schemas/package.schema.json b/extensions/html-language-features/schemas/package.schema.json
index 205143c..cc9f918 100644
--- a/extensions/html-language-features/schemas/package.schema.json
+++ b/extensions/html-language-features/schemas/package.schema.json
@@ -9,3 +9,3 @@
 					"type": "array",
-					"markdownDescription": "A list of relative file paths pointing to JSON files following the [custom data format](https://github.com/microsoft/vscode-html-languageservice/blob/master/docs/customData.md).\n\nVS Code loads custom data on startup to enhance its HTML support for the custom HTML tags, attributes and attribute values you specify in the JSON files.\n\nThe file paths are relative to workspace and only workspace folder settings are considered.",
+					"markdownDescription": "A list of relative file paths pointing to JSON files following the [custom data format](https://github.com/microsoft/vscode-html-languageservice/blob/master/docs/customData.md).\n\n!!APP_NAME!! loads custom data on startup to enhance its HTML support for the custom HTML tags, attributes and attribute values you specify in the JSON files.\n\nThe file paths are relative to workspace and only workspace folder settings are considered.",
 					"items": {
diff --git a/extensions/jake/package.nls.json b/extensions/jake/package.nls.json
index e82030e..ac999f6 100644
--- a/extensions/jake/package.nls.json
+++ b/extensions/jake/package.nls.json
@@ -1,4 +1,4 @@
 {
-	"description": "Extension to add Jake capabilities to VS Code.",
-	"displayName": "Jake support for VS Code",
+	"description": "Extension to add Jake capabilities to !!APP_NAME!!.",
+	"displayName": "Jake support for !!APP_NAME!!",
 	"jake.taskDefinition.type.description": "The Jake task to customize.",
diff --git a/extensions/json-language-features/package.nls.json b/extensions/json-language-features/package.nls.json
index af6c9d8..36e64cf 100644
--- a/extensions/json-language-features/package.nls.json
+++ b/extensions/json-language-features/package.nls.json
@@ -11,3 +11,3 @@
 	"json.validate.enable.desc": "Enable/disable JSON validation.",
-	"json.tracing.desc": "Traces the communication between VS Code and the JSON language server.",
+	"json.tracing.desc": "Traces the communication between !!APP_NAME!! and the JSON language server.",
 	"json.colorDecorators.enable.desc": "Enables or disables color decorators",
diff --git a/extensions/markdown-language-features/package.nls.json b/extensions/markdown-language-features/package.nls.json
index fe98103..ca36e86 100644
--- a/extensions/markdown-language-features/package.nls.json
+++ b/extensions/markdown-language-features/package.nls.json
@@ -22,3 +22,3 @@
 	"markdown.trace.extension.desc": "Enable debug logging for the Markdown extension.",
-	"markdown.trace.server.desc": "Traces the communication between VS Code and the Markdown language server.",
+	"markdown.trace.server.desc": "Traces the communication between !!APP_NAME!! and the Markdown language server.",
 	"markdown.server.log.desc": "Controls the logging level of the Markdown language server.",
@@ -76,3 +76,3 @@
 		"comment": [
-			"This setting is use the user drops or pastes image data into the editor. In this case, VS Code automatically creates a new image file in the workspace containing the dropped/pasted image.",
+			"This setting is use the user drops or pastes image data into the editor. In this case, !!APP_NAME!! automatically creates a new image file in the workspace containing the dropped/pasted image.",
 			"It's easier to explain this setting with an example. For example, let's say the setting value was:",
diff --git a/extensions/media-preview/package.nls.json b/extensions/media-preview/package.nls.json
index 920ced7..755a166 100644
--- a/extensions/media-preview/package.nls.json
+++ b/extensions/media-preview/package.nls.json
@@ -2,3 +2,3 @@
 	"displayName": "Media Preview",
-	"description": "Provides VS Code's built-in previews for images, audio, and video",
+	"description": "Provides !!APP_NAME!!'s built-in previews for images, audio, and video",
 	"customEditor.audioPreview.displayName": "Audio Preview",
diff --git a/extensions/media-preview/src/audioPreview.ts b/extensions/media-preview/src/audioPreview.ts
index 5058f7e..ee1283a 100644
--- a/extensions/media-preview/src/audioPreview.ts
+++ b/extensions/media-preview/src/audioPreview.ts
@@ -82,3 +82,3 @@ class AudioPreview extends MediaPreview {
 		<p>${vscode.l10n.t("An error occurred while loading the audio file.")}</p>
-		<a href="#" class="open-file-link">${vscode.l10n.t("Open file using VS Code's standard text/binary editor?")}</a>
+		<a href="#" class="open-file-link">${vscode.l10n.t("Open file using !!APP_NAME!!'s standard text/binary editor?")}</a>
 	</div>
diff --git a/extensions/media-preview/src/imagePreview/index.ts b/extensions/media-preview/src/imagePreview/index.ts
index b405cd6..8eafea3 100644
--- a/extensions/media-preview/src/imagePreview/index.ts
+++ b/extensions/media-preview/src/imagePreview/index.ts
@@ -209,3 +209,3 @@ class ImagePreview extends MediaPreview {
 		<p>${vscode.l10n.t("An error occurred while loading the image.")}</p>
-		<a href="#" class="open-file-link">${vscode.l10n.t("Open file using VS Code's standard text/binary editor?")}</a>
+		<a href="#" class="open-file-link">${vscode.l10n.t("Open file using !!APP_NAME!!'s standard text/binary editor?")}</a>
 	</div>
diff --git a/extensions/media-preview/src/videoPreview.ts b/extensions/media-preview/src/videoPreview.ts
index 6701212..c9f9fb2 100644
--- a/extensions/media-preview/src/videoPreview.ts
+++ b/extensions/media-preview/src/videoPreview.ts
@@ -86,3 +86,3 @@ class VideoPreview extends MediaPreview {
 		<p>${vscode.l10n.t("An error occurred while loading the video file.")}</p>
-		<a href="#" class="open-file-link">${vscode.l10n.t("Open file using VS Code's standard text/binary editor?")}</a>
+		<a href="#" class="open-file-link">${vscode.l10n.t("Open file using !!APP_NAME!!'s standard text/binary editor?")}</a>
 	</div>
diff --git a/extensions/notebook-renderers/package.json b/extensions/notebook-renderers/package.json
index d6ece35..547202f 100644
--- a/extensions/notebook-renderers/package.json
+++ b/extensions/notebook-renderers/package.json
@@ -22,3 +22,3 @@
 				"entrypoint": "./renderer-out/index.js",
-				"displayName": "VS Code Builtin Notebook Output Renderer",
+				"displayName": "!!APP_NAME!! Builtin Notebook Output Renderer",
 				"requiresMessaging": "never",
diff --git a/extensions/npm/package.nls.json b/extensions/npm/package.nls.json
index 56a77ff..e9f32d1 100644
--- a/extensions/npm/package.nls.json
+++ b/extensions/npm/package.nls.json
@@ -2,3 +2,3 @@
 	"description": "Extension to add task support for npm scripts.",
-	"displayName": "NPM support for VS Code",
+	"displayName": "NPM support for !!APP_NAME!!",
 	"workspaceTrust": "This extension executes tasks, which require trust to run.",
diff --git a/extensions/swift/syntaxes/swift.tmLanguage.json b/extensions/swift/syntaxes/swift.tmLanguage.json
index 7d6694c..83b2599 100644
--- a/extensions/swift/syntaxes/swift.tmLanguage.json
+++ b/extensions/swift/syntaxes/swift.tmLanguage.json
@@ -260,3 +260,3 @@
 				{
-					"comment": "The simpler (?<=\\bProcess\\.|\\bCommandLine\\.) breaks VS Code / Atom, see https://github.com/textmate/swift.tmbundle/issues/29",
+					"comment": "The simpler (?<=\\bProcess\\.|\\bCommandLine\\.) breaks !!APP_NAME!! / Atom, see https://github.com/textmate/swift.tmbundle/issues/29",
 					"name": "support.variable.swift",
diff --git a/extensions/typescript-language-features/package.nls.json b/extensions/typescript-language-features/package.nls.json
index 5de96a8..563d7eb 100644
--- a/extensions/typescript-language-features/package.nls.json
+++ b/extensions/typescript-language-features/package.nls.json
@@ -80,4 +80,4 @@
 	"configuration.tsserver.experimental.enableProjectDiagnostics": "Enables project wide error reporting.",
-	"typescript.locale": "Sets the locale used to report JavaScript and TypeScript errors. Defaults to use VS Code's locale.",
-	"typescript.locale.auto": "Use VS Code's configured display language.",
+	"typescript.locale": "Sets the locale used to report JavaScript and TypeScript errors. Defaults to use !!APP_NAME!!'s locale.",
+	"typescript.locale.auto": "Use !!APP_NAME!!'s configured display language.",
 	"configuration.implicitProjectConfig.module": "Sets the module system for the program. See more: https://www.typescriptlang.org/tsconfig#module.",
@@ -163,3 +163,3 @@
 	"typescript.workspaceSymbols.excludeLibrarySymbols": "Exclude symbols that come from library files in `Go to Symbol in Workspace` results. Requires using TypeScript 5.3+ in the workspace.",
-	"typescript.updateImportsOnFileMove.enabled": "Enable/disable automatic updating of import paths when you rename or move a file in VS Code.",
+	"typescript.updateImportsOnFileMove.enabled": "Enable/disable automatic updating of import paths when you rename or move a file in !!APP_NAME!!.",
 	"typescript.updateImportsOnFileMove.enabled.prompt": "Prompt on each rename.",
@@ -170,5 +170,5 @@
 	"configuration.suggest.completeJSDocs": "Enable/disable suggestion to complete JSDoc comments.",
-	"configuration.tsserver.useVsCodeWatcher": "Use VS Code's file watchers instead of TypeScript's. Requires using TypeScript 5.4+ in the workspace.",
+	"configuration.tsserver.useVsCodeWatcher": "Use !!APP_NAME!!'s file watchers instead of TypeScript's. Requires using TypeScript 5.4+ in the workspace.",
 	"configuration.tsserver.watchOptions": "Configure which watching strategies should be used to keep track of files and directories.",
-	"configuration.tsserver.watchOptions.vscode": "Use VS Code's file watchers instead of TypeScript's. Requires using TypeScript 5.4+ in the workspace.",
+	"configuration.tsserver.watchOptions.vscode": "Use !!APP_NAME!!'s file watchers instead of TypeScript's. Requires using TypeScript 5.4+ in the workspace.",
 	"configuration.tsserver.watchOptions.watchFile": "Strategy for how individual files are watched.",
@@ -222,6 +222,6 @@
 	"configuration.suggest.objectLiteralMethodSnippets.enabled": "Enable/disable snippet completions for methods in object literals.",
-	"configuration.tsserver.web.projectWideIntellisense.enabled": "Enable/disable project-wide IntelliSense on web. Requires that VS Code is running in a trusted context.",
+	"configuration.tsserver.web.projectWideIntellisense.enabled": "Enable/disable project-wide IntelliSense on web. Requires that !!APP_NAME!! is running in a trusted context.",
 	"configuration.tsserver.web.projectWideIntellisense.suppressSemanticErrors": "Suppresses semantic errors on web even when project wide IntelliSense is enabled. This is always on when project wide IntelliSense is not enabled or available. See `#typescript.tsserver.web.projectWideIntellisense.enabled#`",
 	"configuration.tsserver.web.typeAcquisition.enabled": "Enable/disable package acquisition on the web. This enables IntelliSense for imported packages. Requires `#typescript.tsserver.web.projectWideIntellisense.enabled#`. Currently not supported for Safari.",
-	"configuration.tsserver.nodePath": "Run TS Server on a custom Node installation. This can be a path to a Node executable, or 'node' if you want VS Code to detect a Node installation.",
+	"configuration.tsserver.nodePath": "Run TS Server on a custom Node installation. This can be a path to a Node executable, or 'node' if you want !!APP_NAME!! to detect a Node installation.",
 	"configuration.updateImportsOnPaste": "Automatically update imports when pasting code. Requires TypeScript 5.6+.",
@@ -238,7 +238,7 @@
 	"walkthroughs.nodejsWelcome.debugJsFile.title": "Run and Debug your JavaScript",
-	"walkthroughs.nodejsWelcome.debugJsFile.description": "Once you've installed Node.js, you can run JavaScript programs at a terminal by entering ``node your-file-name.js``\nAnother easy way to run Node.js programs is by using VS Code's debugger which lets you run your code, pause at different points, and help you understand what's going on step-by-step.\n[Start Debugging](command:javascript-walkthrough.commands.debugJsFile)",
-	"walkthroughs.nodejsWelcome.debugJsFile.altText": "Debug and run your JavaScript code in Node.js with Visual Studio Code.",
+	"walkthroughs.nodejsWelcome.debugJsFile.description": "Once you've installed Node.js, you can run JavaScript programs at a terminal by entering ``node your-file-name.js``\nAnother easy way to run Node.js programs is by using !!APP_NAME!!'s debugger which lets you run your code, pause at different points, and help you understand what's going on step-by-step.\n[Start Debugging](command:javascript-walkthrough.commands.debugJsFile)",
+	"walkthroughs.nodejsWelcome.debugJsFile.altText": "Debug and run your JavaScript code in Node.js with !!APP_NAME!!.",
 	"walkthroughs.nodejsWelcome.learnMoreAboutJs.title": "Explore More",
-	"walkthroughs.nodejsWelcome.learnMoreAboutJs.description": "Want to get more comfortable with JavaScript, Node.js, and VS Code? Be sure to check out our docs!\nWe've got lots of resources for learning [JavaScript](https://code.visualstudio.com/docs/nodejs/working-with-javascript) and [Node.js](https://code.visualstudio.com/docs/nodejs/nodejs-tutorial).\n\n[Learn More](https://code.visualstudio.com/docs/nodejs/nodejs-tutorial)",
-	"walkthroughs.nodejsWelcome.learnMoreAboutJs.altText": "Learn more about JavaScript and Node.js in Visual Studio Code."
+	"walkthroughs.nodejsWelcome.learnMoreAboutJs.description": "Want to get more comfortable with JavaScript, Node.js, and !!APP_NAME!!? Be sure to check out our docs!\nWe've got lots of resources for learning [JavaScript](https://code.visualstudio.com/docs/nodejs/working-with-javascript) and [Node.js](https://code.visualstudio.com/docs/nodejs/nodejs-tutorial).\n\n[Learn More](https://code.visualstudio.com/docs/nodejs/nodejs-tutorial)",
+	"walkthroughs.nodejsWelcome.learnMoreAboutJs.altText": "Learn more about JavaScript and Node.js in !!APP_NAME!!."
 }
diff --git a/extensions/typescript-language-features/src/tsServer/versionManager.ts b/extensions/typescript-language-features/src/tsServer/versionManager.ts
index 43a2413..ba7934f 100644
--- a/extensions/typescript-language-features/src/tsServer/versionManager.ts
+++ b/extensions/typescript-language-features/src/tsServer/versionManager.ts
@@ -100,3 +100,3 @@ export class TypeScriptVersionManager extends Disposable {
 				? '• '
-				: '') + vscode.l10n.t("Use VS Code's Version"),
+				: '') + vscode.l10n.t("Use !!APP_NAME!!'s Version"),
 			description: bundledVersion.displayName,
diff --git a/extensions/typescript-language-features/src/tsServer/versionProvider.electron.ts b/extensions/typescript-language-features/src/tsServer/versionProvider.electron.ts
index 239519e..5e58d4d 100644
--- a/extensions/typescript-language-features/src/tsServer/versionProvider.electron.ts
+++ b/extensions/typescript-language-features/src/tsServer/versionProvider.electron.ts
@@ -70,3 +70,3 @@ export class DiskTypeScriptVersionProvider implements ITypeScriptVersionProvider
 
-		vscode.window.showErrorMessage(vscode.l10n.t("VS Code\'s tsserver was deleted by another application such as a misbehaving virus detection tool. Please reinstall VS Code."));
+		vscode.window.showErrorMessage(vscode.l10n.t("!!APP_NAME!!\'s tsserver was deleted by another application such as a misbehaving virus detection tool. Please reinstall !!APP_NAME!!."));
 		throw new Error('Could not find bundled tsserver.js');
diff --git a/extensions/typescript-language-features/src/tsconfig.ts b/extensions/typescript-language-features/src/tsconfig.ts
index 01a88a4..1fdc86e 100644
--- a/extensions/typescript-language-features/src/tsconfig.ts
+++ b/extensions/typescript-language-features/src/tsconfig.ts
@@ -155,3 +155,3 @@ export async function openProjectConfigForFile(
 		vscode.window.showInformationMessage(
-			vscode.l10n.t("Please open a folder in VS Code to use a TypeScript or JavaScript project"));
+			vscode.l10n.t("Please open a folder in !!APP_NAME!! to use a TypeScript or JavaScript project"));
 		return;
diff --git a/extensions/typescript-language-features/src/typescriptServiceClient.ts b/extensions/typescript-language-features/src/typescriptServiceClient.ts
index 4201d6d..ded6803 100644
--- a/extensions/typescript-language-features/src/typescriptServiceClient.ts
+++ b/extensions/typescript-language-features/src/typescriptServiceClient.ts
@@ -653,3 +653,3 @@ export default class TypeScriptServiceClient extends Disposable implements IType
 						prompt = vscode.window.showErrorMessage<vscode.MessageItem>(
-							vscode.l10n.t("The JS/TS language service immediately crashed 5 times. The service will not be restarted.\nThis may be caused by a plugin contributed by one of these extensions: {0}.\nPlease try disabling these extensions before filing an issue against VS Code.", pluginExtensionList));
+							vscode.l10n.t("The JS/TS language service immediately crashed 5 times. The service will not be restarted.\nThis may be caused by a plugin contributed by one of these extensions: {0}.\nPlease try disabling these extensions before filing an issue against !!APP_NAME!!.", pluginExtensionList));
 					} else {
@@ -674,3 +674,3 @@ export default class TypeScriptServiceClient extends Disposable implements IType
 							prompt = vscode.window.showWarningMessage<vscode.MessageItem>(
-								vscode.l10n.t("The JS/TS language service crashed 5 times in the last 5 Minutes.\nThis may be caused by a plugin contributed by one of these extensions: {0}\nPlease try disabling these extensions before filing an issue against VS Code.", pluginExtensionList));
+								vscode.l10n.t("The JS/TS language service crashed 5 times in the last 5 Minutes.\nThis may be caused by a plugin contributed by one of these extensions: {0}\nPlease try disabling these extensions before filing an issue against !!APP_NAME!!.", pluginExtensionList));
 						} else {
@@ -688,3 +688,3 @@ export default class TypeScriptServiceClient extends Disposable implements IType
 						prompt = vscode.window.showWarningMessage<vscode.MessageItem>(
-							vscode.l10n.t("The JS/TS language service crashed.\nThis may be caused by a plugin contributed by one of these extensions: {0}.\nPlease try disabling these extensions before filing an issue against VS Code.", pluginExtensionList));
+							vscode.l10n.t("The JS/TS language service crashed.\nThis may be caused by a plugin contributed by one of these extensions: {0}.\nPlease try disabling these extensions before filing an issue against !!APP_NAME!!.", pluginExtensionList));
 					} else {
diff --git a/extensions/vscode-api-tests/package.json b/extensions/vscode-api-tests/package.json
index 440d495..41b94c3 100644
--- a/extensions/vscode-api-tests/package.json
+++ b/extensions/vscode-api-tests/package.json
@@ -2,3 +2,3 @@
   "name": "vscode-api-tests",
-  "description": "API tests for VS Code",
+  "description": "API tests for !!APP_NAME!!",
   "version": "0.0.1",
diff --git a/extensions/vscode-colorize-tests/package.json b/extensions/vscode-colorize-tests/package.json
index 4959276..41d5ae7 100644
--- a/extensions/vscode-colorize-tests/package.json
+++ b/extensions/vscode-colorize-tests/package.json
@@ -2,3 +2,3 @@
   "name": "vscode-colorize-tests",
-  "description": "Colorize tests for VS Code",
+  "description": "Colorize tests for !!APP_NAME!!",
   "version": "0.0.1",
diff --git a/extensions/vscode-colorize-tests/test/colorize-fixtures/14119.less b/extensions/vscode-colorize-tests/test/colorize-fixtures/14119.less
index a0006d8..132b67e 100644
--- a/extensions/vscode-colorize-tests/test/colorize-fixtures/14119.less
+++ b/extensions/vscode-colorize-tests/test/colorize-fixtures/14119.less
@@ -1,2 +1,2 @@
-#f(@hm: "broken highlighting in VS Code") {
+#f(@hm: "broken highlighting in !!APP_NAME!!") {
     content: "";
diff --git a/extensions/vscode-colorize-tests/test/colorize-results/14119_less.json b/extensions/vscode-colorize-tests/test/colorize-results/14119_less.json
index 6680753..da1795a 100644
--- a/extensions/vscode-colorize-tests/test/colorize-results/14119_less.json
+++ b/extensions/vscode-colorize-tests/test/colorize-results/14119_less.json
@@ -114,3 +114,3 @@
 	{
-		"c": "broken highlighting in VS Code",
+		"c": "broken highlighting in !!APP_NAME!!",
 		"t": "source.css.less meta.selector.less meta.group.less meta.property-value.less string.quoted.double.less",
diff --git a/extensions/vscode-test-resolver/package.json b/extensions/vscode-test-resolver/package.json
index c96c1d5..3288347 100644
--- a/extensions/vscode-test-resolver/package.json
+++ b/extensions/vscode-test-resolver/package.json
@@ -2,3 +2,3 @@
   "name": "vscode-test-resolver",
-  "description": "Test resolver for VS Code",
+  "description": "Test resolver for !!APP_NAME!!",
   "version": "0.0.1",
diff --git a/extensions/vscode-test-resolver/src/download.ts b/extensions/vscode-test-resolver/src/download.ts
index a351aa7..c32e3ef 100644
--- a/extensions/vscode-test-resolver/src/download.ts
+++ b/extensions/vscode-test-resolver/src/download.ts
@@ -32,3 +32,3 @@ async function downloadVSCodeServerArchive(updateUrl: string, commit: string, qu
 	return new Promise((resolve, reject) => {
-		log(`Downloading VS Code Server from: ${downloadUrl}`);
+		log(`Downloading !!APP_NAME!! Server from: ${downloadUrl}`);
 		const requestOptions: https.RequestOptions = parseUrl(downloadUrl);
@@ -37,3 +37,3 @@ async function downloadVSCodeServerArchive(updateUrl: string, commit: string, qu
 			if (res.statusCode !== 302) {
-				reject('Failed to get VS Code server archive location');
+				reject('Failed to get !!APP_NAME!! server archive location');
 				res.resume(); // read the rest of the response data and discard it
@@ -43,3 +43,3 @@ async function downloadVSCodeServerArchive(updateUrl: string, commit: string, qu
 			if (!archiveUrl) {
-				reject('Failed to get VS Code server archive location');
+				reject('Failed to get !!APP_NAME!! server archive location');
 				res.resume(); // read the rest of the response data and discard it
@@ -68,3 +68,3 @@ async function downloadVSCodeServerArchive(updateUrl: string, commit: string, qu
 /**
- * Unzip a .zip or .tar.gz VS Code archive
+ * Unzip a .zip or .tar.gz !!APP_NAME!! archive
  */
@@ -102,3 +102,3 @@ export async function downloadAndUnzipVSCodeServer(updateUrl: string, commit: st
 	} else {
-		log(`Downloading VS Code Server ${quality} - ${commit} into ${extractDir}.`);
+		log(`Downloading !!APP_NAME!! Server ${quality} - ${commit} into ${extractDir}.`);
 		try {
@@ -111,3 +111,3 @@ export async function downloadAndUnzipVSCodeServer(updateUrl: string, commit: st
 		} catch (err) {
-			throw Error(`Failed to download and unzip VS Code ${quality} - ${commit}`);
+			throw Error(`Failed to download and unzip !!APP_NAME!! ${quality} - ${commit}`);
 		}
diff --git a/extensions/vscode-test-resolver/src/extension.ts b/extensions/vscode-test-resolver/src/extension.ts
index 2fab3ec..1195e3d 100644
--- a/extensions/vscode-test-resolver/src/extension.ts
+++ b/extensions/vscode-test-resolver/src/extension.ts
@@ -178,3 +178,3 @@ export function activate(context: vscode.ExtensionContext) {
 					const serverBin = path.join(remoteDataDir, 'bin');
-					progress.report({ message: 'Installing VSCode Server' });
+					progress.report({ message: 'Installing !!APP_NAME!! Server' });
 					serverLocation = await downloadAndUnzipVSCodeServer(updateUrl, commit, quality, serverBin, m => outputChannel.appendLine(m));
diff --git a/src/main.ts b/src/main.ts
index fc3b6ad..0675813 100644
--- a/src/main.ts
+++ b/src/main.ts
@@ -407,3 +407,3 @@ function createDefaultArgvConfigSync(argvConfigPath: string): void {
 		const defaultArgvConfigContent = [
-			'// This configuration file allows you to pass permanent command line arguments to VS Code.',
+			'// This configuration file allows you to pass permanent command line arguments to !!APP_NAME!!.',
 			'// Only a subset of arguments is currently supported to reduce the likelihood of breaking',
@@ -413,6 +413,6 @@ function createDefaultArgvConfigSync(argvConfigPath: string): void {
 			'//',
-			'// NOTE: Changing this file requires a restart of VS Code.',
+			'// NOTE: Changing this file requires a restart of !!APP_NAME!!.',
 			'{',
 			'	// Use software rendering instead of hardware accelerated rendering.',
-			'	// This can help in cases where you see rendering issues in VS Code.',
+			'	// This can help in cases where you see rendering issues in !!APP_NAME!!.',
 			'	// "disable-hardware-acceleration": true',
diff --git a/src/vs/code/electron-main/app.ts b/src/vs/code/electron-main/app.ts
index d045e6a..f9d8036 100644
--- a/src/vs/code/electron-main/app.ts
+++ b/src/vs/code/electron-main/app.ts
@@ -526,3 +526,3 @@ export class CodeApplication extends Disposable {
 	async startup(): Promise<void> {
-		this.logService.debug('Starting VS Code');
+		this.logService.debug('Starting !!APP_NAME!!');
 		this.logService.debug(`from: ${this.environmentMainService.appRoot}`);
diff --git a/src/vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.ts b/src/vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.ts
index a200bf8..425936a 100644
--- a/src/vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.ts
+++ b/src/vs/editor/contrib/toggleTabFocusMode/browser/toggleTabFocusMode.ts
@@ -19,3 +19,3 @@ export class ToggleTabFocusModeAction extends Action2 {
 			id: ToggleTabFocusModeAction.ID,
-			title: nls.localize2({ key: 'toggle.tabMovesFocus', comment: ['Turn on/off use of tab key for moving focus around VS Code'] }, 'Toggle Tab Key Moves Focus'),
+			title: nls.localize2({ key: 'toggle.tabMovesFocus', comment: ['Turn on/off use of tab key for moving focus around !!APP_NAME!!'] }, 'Toggle Tab Key Moves Focus'),
 			precondition: undefined,
diff --git a/src/vs/platform/contextkey/common/contextkeys.ts b/src/vs/platform/contextkey/common/contextkeys.ts
index c256dba..10a79c8 100644
--- a/src/vs/platform/contextkey/common/contextkeys.ts
+++ b/src/vs/platform/contextkey/common/contextkeys.ts
@@ -19,3 +19,3 @@ export const IsMobileContext = new RawContextKey<boolean>('isMobile', isMobile,
 export const IsDevelopmentContext = new RawContextKey<boolean>('isDevelopment', false, true);
-export const ProductQualityContext = new RawContextKey<string>('productQualityType', '', localize('productQualityType', "Quality type of VS Code"));
+export const ProductQualityContext = new RawContextKey<string>('productQualityType', '', localize('productQualityType', "Quality type of !!APP_NAME!!"));
 
diff --git a/src/vs/platform/extensionManagement/node/extensionManagementService.ts b/src/vs/platform/extensionManagement/node/extensionManagementService.ts
index 0a50a2e..0dacd44 100644
--- a/src/vs/platform/extensionManagement/node/extensionManagementService.ts
+++ b/src/vs/platform/extensionManagement/node/extensionManagementService.ts
@@ -153,3 +153,3 @@ export class ExtensionManagementService extends AbstractExtensionManagementServi
 			if (manifest.engines && manifest.engines.vscode && !isEngineValid(manifest.engines.vscode, this.productService.version, this.productService.date)) {
-				throw new Error(nls.localize('incompatible', "Unable to install extension '{0}' as it is not compatible with VS Code '{1}'.", extensionId, this.productService.version));
+				throw new Error(nls.localize('incompatible', "Unable to install extension '{0}' as it is not compatible with !!APP_NAME!! '{1}'.", extensionId, this.productService.version));
 			}
@@ -1028,3 +1028,3 @@ class InstallExtensionInProfileTask extends AbstractExtensionTask<ILocalExtensio
 					} catch (e) {
-						throw new Error(nls.localize('restartCode', "Please restart VS Code before reinstalling {0}.", this.manifest.displayName || this.manifest.name));
+						throw new Error(nls.localize('restartCode', "Please restart !!APP_NAME!! before reinstalling {0}.", this.manifest.displayName || this.manifest.name));
 					}
@@ -1039,3 +1039,3 @@ class InstallExtensionInProfileTask extends AbstractExtensionTask<ILocalExtensio
 				} catch (e) {
-					throw new Error(nls.localize('restartCode', "Please restart VS Code before reinstalling {0}.", this.manifest.displayName || this.manifest.name));
+					throw new Error(nls.localize('restartCode', "Please restart !!APP_NAME!! before reinstalling {0}.", this.manifest.displayName || this.manifest.name));
 				}
diff --git a/src/vs/platform/extensions/common/extensionValidator.ts b/src/vs/platform/extensions/common/extensionValidator.ts
index 87a9288..dcbbf0a 100644
--- a/src/vs/platform/extensions/common/extensionValidator.ts
+++ b/src/vs/platform/extensions/common/extensionValidator.ts
@@ -374,5 +374,5 @@ export function areApiProposalsCompatible(apiProposals: string[], arg1?: any): b
 			if (incompatibleProposals.length === 1) {
-				notices.push(nls.localize('apiProposalMismatch1', "This extension is using the API proposal '{0}' that is not compatible with the current version of VS Code.", incompatibleProposals[0]));
+				notices.push(nls.localize('apiProposalMismatch1', "This extension is using the API proposal '{0}' that is not compatible with the current version of !!APP_NAME!!.", incompatibleProposals[0]));
 			} else {
-				notices.push(nls.localize('apiProposalMismatch2', "This extension is using the API proposals {0} and '{1}' that are not compatible with the current version of VS Code.",
+				notices.push(nls.localize('apiProposalMismatch2', "This extension is using the API proposals {0} and '{1}' that are not compatible with the current version of !!APP_NAME!!.",
 					incompatibleProposals.slice(0, incompatibleProposals.length - 1).map(p => `'${p}'`).join(', '),
diff --git a/src/vs/platform/externalTerminal/node/externalTerminalService.ts b/src/vs/platform/externalTerminal/node/externalTerminalService.ts
index ca6c82b..56fab2c 100644
--- a/src/vs/platform/externalTerminal/node/externalTerminalService.ts
+++ b/src/vs/platform/externalTerminal/node/externalTerminalService.ts
@@ -17,3 +17,3 @@ import { ITerminalEnvironment } from '../../terminal/common/terminal.js';
 
-const TERMINAL_TITLE = nls.localize('console.title', "VS Code Console");
+const TERMINAL_TITLE = nls.localize('console.title', "!!APP_NAME!! Console");
 
diff --git a/src/vs/platform/terminal/common/terminalPlatformConfiguration.ts b/src/vs/platform/terminal/common/terminalPlatformConfiguration.ts
index 7d8d78b..d39dad4 100644
--- a/src/vs/platform/terminal/common/terminalPlatformConfiguration.ts
+++ b/src/vs/platform/terminal/common/terminalPlatformConfiguration.ts
@@ -338,3 +338,3 @@ const terminalPlatformConfiguration: IConfigurationNode = {
 			scope: ConfigurationScope.APPLICATION,
-			description: localize('terminal.integrated.inheritEnv', "Whether new shells should inherit their environment from VS Code, which may source a login shell to ensure $PATH and other development variables are initialized. This has no effect on Windows."),
+			description: localize('terminal.integrated.inheritEnv', "Whether new shells should inherit their environment from !!APP_NAME!!, which may source a login shell to ensure $PATH and other development variables are initialized. This has no effect on Windows."),
 			type: 'boolean',
diff --git a/src/vs/platform/update/common/update.config.contribution.ts b/src/vs/platform/update/common/update.config.contribution.ts
index d96926b..a787b34 100644
--- a/src/vs/platform/update/common/update.config.contribution.ts
+++ b/src/vs/platform/update/common/update.config.contribution.ts
@@ -47,3 +47,3 @@ configurationRegistry.registerConfiguration({
 			title: localize('enableWindowsBackgroundUpdatesTitle', "Enable Background Updates on Windows"),
-			description: localize('enableWindowsBackgroundUpdates', "Enable to download and install new VS Code versions in the background on Windows."),
+			description: localize('enableWindowsBackgroundUpdates', "Enable to download and install new !!APP_NAME!! versions in the background on Windows."),
 			included: isWindows && !isWeb
diff --git a/src/vs/platform/update/electron-main/abstractUpdateService.ts b/src/vs/platform/update/electron-main/abstractUpdateService.ts
index 48d0d86..cbd5a33 100644
--- a/src/vs/platform/update/electron-main/abstractUpdateService.ts
+++ b/src/vs/platform/update/electron-main/abstractUpdateService.ts
@@ -23,3 +23,3 @@ export type UpdateErrorClassification = {
 	messageHash: { classification: 'SystemMetaData'; purpose: 'FeatureInsight'; comment: 'The hash of the error message.' };
-	comment: 'This is used to know how often VS Code updates have failed.';
+	comment: 'This is used to know how often !!APP_NAME!! updates have failed.';
 };
diff --git a/src/vs/platform/update/electron-main/updateService.darwin.ts b/src/vs/platform/update/electron-main/updateService.darwin.ts
index b78ebc5..9b0eb56 100644
--- a/src/vs/platform/update/electron-main/updateService.darwin.ts
+++ b/src/vs/platform/update/electron-main/updateService.darwin.ts
@@ -122,4 +122,4 @@ export class DarwinUpdateService extends AbstractUpdateService implements IRelau
 			owner: 'joaomoreno';
-			newVersion: { classification: 'SystemMetaData'; purpose: 'FeatureInsight'; comment: 'The version number of the new VS Code that has been downloaded.' };
-			comment: 'This is used to know how often VS Code has successfully downloaded the update.';
+			newVersion: { classification: 'SystemMetaData'; purpose: 'FeatureInsight'; comment: 'The version number of the new !!APP_NAME!! that has been downloaded.' };
+			comment: 'This is used to know how often !!APP_NAME!! has successfully downloaded the update.';
 		};
diff --git a/src/vs/server/node/server.cli.ts b/src/vs/server/node/server.cli.ts
index 0535ddd..2478997 100644
--- a/src/vs/server/node/server.cli.ts
+++ b/src/vs/server/node/server.cli.ts
@@ -469,3 +469,3 @@ function asExtensionIdOrVSIX(inputs: string[] | undefined) {
 function fatal(message: string, err: any): void {
-	console.error('Unable to connect to VS Code server: ' + message);
+	console.error('Unable to connect to !!APP_NAME!! server: ' + message);
 	console.error(err);
diff --git a/src/vs/workbench/api/browser/mainThreadAuthentication.ts b/src/vs/workbench/api/browser/mainThreadAuthentication.ts
index 8e931d0..919057a 100644
--- a/src/vs/workbench/api/browser/mainThreadAuthentication.ts
+++ b/src/vs/workbench/api/browser/mainThreadAuthentication.ts
@@ -398,3 +398,3 @@ export class MainThreadAuthentication extends Disposable implements MainThreadAu
 				owner: 'TylerLeonhardt';
-				comment: 'Used to see which extensions are using the VSCode client id override';
+				comment: 'Used to see which extensions are using the !!APP_NAME!! client id override';
 				extensionId: { classification: 'SystemMetaData'; purpose: 'FeatureInsight'; comment: 'The extension id.' };
diff --git a/src/vs/workbench/api/browser/viewsExtensionPoint.ts b/src/vs/workbench/api/browser/viewsExtensionPoint.ts
index fdf354d..b196297 100644
--- a/src/vs/workbench/api/browser/viewsExtensionPoint.ts
+++ b/src/vs/workbench/api/browser/viewsExtensionPoint.ts
@@ -43,3 +43,3 @@ const viewsContainerSchema: IJSONSchema = {
 		id: {
-			description: localize({ key: 'vscode.extension.contributes.views.containers.id', comment: ['Contribution refers to those that an extension contributes to VS Code through an extension/contribution point. '] }, "Unique id used to identify the container in which views can be contributed using 'views' contribution point"),
+			description: localize({ key: 'vscode.extension.contributes.views.containers.id', comment: ['Contribution refers to those that an extension contributes to !!APP_NAME!! through an extension/contribution point. '] }, "Unique id used to identify the container in which views can be contributed using 'views' contribution point"),
 			type: 'string',
diff --git a/src/vs/workbench/api/common/extHostApiCommands.ts b/src/vs/workbench/api/common/extHostApiCommands.ts
index 436e350..f426464 100644
--- a/src/vs/workbench/api/common/extHostApiCommands.ts
+++ b/src/vs/workbench/api/common/extHostApiCommands.ts
@@ -443,3 +443,3 @@ const newCommands: ApiCommand[] = [
 			ApiCommandArgument.Uri.with('resource', 'Resource to open'),
-			ApiCommandArgument.String.with('viewId', 'Custom editor view id. This should be the viewType string for custom editors or the notebookType string for notebooks. Use \'default\' to use VS Code\'s default text editor'),
+			ApiCommandArgument.String.with('viewId', 'Custom editor view id. This should be the viewType string for custom editors or the notebookType string for notebooks. Use \'default\' to use !!APP_NAME!!\'s default text editor'),
 			new ApiCommandArgument<vscode.ViewColumn | typeConverters.TextEditorOpenOptions | undefined, [vscode.ViewColumn?, ITextEditorOptions?] | undefined>('columnOrOptions', 'Either the column in which to open or editor options, see vscode.TextDocumentShowOptions',
diff --git a/src/vs/workbench/api/common/extHostCommands.ts b/src/vs/workbench/api/common/extHostCommands.ts
index 0bda2e8..5179ac6 100644
--- a/src/vs/workbench/api/common/extHostCommands.ts
+++ b/src/vs/workbench/api/common/extHostCommands.ts
@@ -462,4 +462,4 @@ export class ApiCommandArgument<V, O = V> {
 	static readonly TypeHierarchyItem = new ApiCommandArgument('item', 'A type hierarchy item', v => v instanceof extHostTypes.TypeHierarchyItem, extHostTypeConverter.TypeHierarchyItem.from);
-	static readonly TestItem = new ApiCommandArgument('testItem', 'A VS Code TestItem', v => v instanceof TestItemImpl, extHostTypeConverter.TestItem.from);
-	static readonly TestProfile = new ApiCommandArgument('testProfile', 'A VS Code test profile', v => v instanceof extHostTypes.TestRunProfileBase, extHostTypeConverter.TestRunProfile.from);
+	static readonly TestItem = new ApiCommandArgument('testItem', 'A !!APP_NAME!! TestItem', v => v instanceof TestItemImpl, extHostTypeConverter.TestItem.from);
+	static readonly TestProfile = new ApiCommandArgument('testProfile', 'A !!APP_NAME!! test profile', v => v instanceof extHostTypes.TestRunProfileBase, extHostTypeConverter.TestRunProfile.from);
 
diff --git a/src/vs/workbench/api/test/browser/extHostNotebook.test.ts b/src/vs/workbench/api/test/browser/extHostNotebook.test.ts
index 0d71384..ae8d169 100644
--- a/src/vs/workbench/api/test/browser/extHostNotebook.test.ts
+++ b/src/vs/workbench/api/test/browser/extHostNotebook.test.ts
@@ -364,3 +364,3 @@ suite('NotebookCell#Document', function () {
 
-	test('Opening a notebook results in VS Code firing the event onDidChangeActiveNotebookEditor twice #118470', function () {
+	test('Opening a notebook results in !!APP_NAME!! firing the event onDidChangeActiveNotebookEditor twice #118470', function () {
 		let count = 0;
diff --git a/src/vs/workbench/browser/actions/helpActions.ts b/src/vs/workbench/browser/actions/helpActions.ts
index 2487213..a22b4d1 100644
--- a/src/vs/workbench/browser/actions/helpActions.ts
+++ b/src/vs/workbench/browser/actions/helpActions.ts
@@ -163,3 +163,3 @@ class OpenNewsletterSignupUrlAction extends Action2 {
 			id: OpenNewsletterSignupUrlAction.ID,
-			title: localize2('newsletterSignup', 'Signup for the VS Code Newsletter'),
+			title: localize2('newsletterSignup', 'Signup for the !!APP_NAME!! Newsletter'),
 			category: Categories.Help,
diff --git a/src/vs/workbench/browser/web.factory.ts b/src/vs/workbench/browser/web.factory.ts
index 0ebe271..20974b2 100644
--- a/src/vs/workbench/browser/web.factory.ts
+++ b/src/vs/workbench/browser/web.factory.ts
@@ -35,3 +35,3 @@ export function create(domElement: HTMLElement, options: IWorkbenchConstructionO
 	if (created) {
-		throw new Error('Unable to create the VSCode workbench more than once.');
+		throw new Error('Unable to create the !!APP_NAME!! workbench more than once.');
 	} else {
diff --git a/src/vs/workbench/browser/workbench.contribution.ts b/src/vs/workbench/browser/workbench.contribution.ts
index 65d3aa3..1a53779 100644
--- a/src/vs/workbench/browser/workbench.contribution.ts
+++ b/src/vs/workbench/browser/workbench.contribution.ts
@@ -696,3 +696,3 @@ const registry = Registry.as<IConfigurationRegistry>(ConfigurationExtensions.Con
 		localize('profileName', "`${profileName}`: name of the profile in which the workspace is opened (e.g. Data Science (Profile)). Ignored if default profile is used."),
-		localize('appName', "`${appName}`: e.g. VS Code."),
+		localize('appName', "`${appName}`: e.g. !!APP_NAME!!."),
 		localize('remoteName', "`${remoteName}`: e.g. SSH"),
diff --git a/src/vs/workbench/contrib/chat/browser/chatSetup.ts b/src/vs/workbench/contrib/chat/browser/chatSetup.ts
index d7b0635..76abf9a 100644
--- a/src/vs/workbench/contrib/chat/browser/chatSetup.ts
+++ b/src/vs/workbench/contrib/chat/browser/chatSetup.ts
@@ -146,3 +146,3 @@ class SetupAgent extends Disposable implements IChatAgentImplementation {
 
-			const { agent, disposable } = SetupAgent.doRegisterAgent(instantiationService, chatAgentService, 'setup.vscode', 'vscode', false, localize2('vscodeAgentDescription', "Ask questions about VS Code").value, ChatAgentLocation.Panel, undefined, context, controller);
+			const { agent, disposable } = SetupAgent.doRegisterAgent(instantiationService, chatAgentService, 'setup.vscode', 'vscode', false, localize2('vscodeAgentDescription', "Ask questions about !!APP_NAME!!").value, ChatAgentLocation.Panel, undefined, context, controller);
 			disposables.add(disposable);
@@ -154,4 +154,4 @@ class SetupAgent extends Disposable implements IChatAgentImplementation {
 				displayName: localize('setupToolDisplayName', "New Workspace"),
-				modelDescription: localize('setupToolsDescription', "Scaffold a new workspace in VS Code"),
-				userDescription: localize('setupToolsDescription', "Scaffold a new workspace in VS Code"),
+				modelDescription: localize('setupToolsDescription', "Scaffold a new workspace in !!APP_NAME!!"),
+				userDescription: localize('setupToolsDescription', "Scaffold a new workspace in !!APP_NAME!!"),
 				canBeReferencedInPrompt: true,
diff --git a/src/vs/workbench/contrib/debug/browser/debugAdapterManager.ts b/src/vs/workbench/contrib/debug/browser/debugAdapterManager.ts
index 0a13063..5a342c0 100644
--- a/src/vs/workbench/contrib/debug/browser/debugAdapterManager.ts
+++ b/src/vs/workbench/contrib/debug/browser/debugAdapterManager.ts
@@ -177,3 +177,3 @@ export class AdapterManager extends Disposable implements IAdapterManager {
 						type: 'number',
-						description: nls.localize('debugServer', "For debug extension development only: if a port is specified VS Code tries to connect to a debug adapter running in server mode"),
+						description: nls.localize('debugServer', "For debug extension development only: if a port is specified !!APP_NAME!! tries to connect to a debug adapter running in server mode"),
 						default: 4711
diff --git a/src/vs/workbench/contrib/extensions/browser/extensions.contribution.ts b/src/vs/workbench/contrib/extensions/browser/extensions.contribution.ts
index 2442c3b..b549091 100644
--- a/src/vs/workbench/contrib/extensions/browser/extensions.contribution.ts
+++ b/src/vs/workbench/contrib/extensions/browser/extensions.contribution.ts
@@ -333,3 +333,3 @@ CommandsRegistry.registerCommand({
 				description: '(optional) Options for installing the extension. Object with the following properties: ' +
-					'`installOnlyNewlyAddedFromExtensionPackVSIX`: When enabled, VS Code installs only newly added extensions from the extension pack VSIX. This option is considered only when installing VSIX. ',
+					'`installOnlyNewlyAddedFromExtensionPackVSIX`: When enabled, !!APP_NAME!! installs only newly added extensions from the extension pack VSIX. This option is considered only when installing VSIX. ',
 				isOptional: true,
@@ -340,3 +340,3 @@ CommandsRegistry.registerCommand({
 							'type': 'boolean',
-							'description': localize('workbench.extensions.installExtension.option.installOnlyNewlyAddedFromExtensionPackVSIX', "When enabled, VS Code installs only newly added extensions from the extension pack VSIX. This option is considered only while installing a VSIX."),
+							'description': localize('workbench.extensions.installExtension.option.installOnlyNewlyAddedFromExtensionPackVSIX', "When enabled, !!APP_NAME!! installs only newly added extensions from the extension pack VSIX. This option is considered only while installing a VSIX."),
 							default: false
@@ -345,3 +345,3 @@ CommandsRegistry.registerCommand({
 							'type': 'boolean',
-							'description': localize('workbench.extensions.installExtension.option.installPreReleaseVersion', "When enabled, VS Code installs the pre-release version of the extension if available."),
+							'description': localize('workbench.extensions.installExtension.option.installPreReleaseVersion', "When enabled, !!APP_NAME!! installs the pre-release version of the extension if available."),
 							default: false
@@ -350,3 +350,3 @@ CommandsRegistry.registerCommand({
 							'type': 'boolean',
-							'description': localize('workbench.extensions.installExtension.option.donotSync', "When enabled, VS Code do not sync this extension when Settings Sync is on."),
+							'description': localize('workbench.extensions.installExtension.option.donotSync', "When enabled, !!APP_NAME!! do not sync this extension when Settings Sync is on."),
 							default: false
@@ -874,4 +874,4 @@ class ExtensionsContributions extends Disposable implements IWorkbenchContributi
 						Severity.Info,
-						vsixs.length > 1 ? localize('InstallVSIXs.successReload', "Completed installing extensions. Please reload Visual Studio Code to enable them.")
-							: localize('InstallVSIXAction.successReload', "Completed installing extension. Please reload Visual Studio Code to enable it."),
+						vsixs.length > 1 ? localize('InstallVSIXs.successReload', "Completed installing extensions. Please reload VSCodium to enable them.")
+							: localize('InstallVSIXAction.successReload', "Completed installing extension. Please reload VSCodium to enable it."),
 						[{
diff --git a/src/vs/workbench/contrib/extensions/browser/extensionsActions.ts b/src/vs/workbench/contrib/extensions/browser/extensionsActions.ts
index 5c24f2d..fafc0d2 100644
--- a/src/vs/workbench/contrib/extensions/browser/extensionsActions.ts
+++ b/src/vs/workbench/contrib/extensions/browser/extensionsActions.ts
@@ -108,3 +108,3 @@ export class PromptExtensionInstallFailureAction extends Action {
 		if (this.error.name === ExtensionManagementErrorCode.Unsupported) {
-			const productName = isWeb ? localize('VS Code for Web', "{0} for the Web", this.productService.nameLong) : this.productService.nameLong;
+			const productName = isWeb ? localize('!!APP_NAME!! for Web', "{0} for the Web", this.productService.nameLong) : this.productService.nameLong;
 			const message = localize('cannot be installed', "The '{0}' extension is not available in {1}. Click 'More Information' to learn more.", this.extension.displayName || this.extension.identifier.id, productName);
@@ -553,3 +553,3 @@ export class InstallAction extends ExtensionAction {
 			} else if (this.extension.deprecationInfo.settings) {
-				detail = localize('deprecated with alternate settings message', "This extension is deprecated as this functionality is now built-in to VS Code.");
+				detail = localize('deprecated with alternate settings message', "This extension is deprecated as this functionality is now built-in to !!APP_NAME!!.");
 
@@ -941,3 +941,3 @@ export class UninstallAction extends ExtensionAction {
 			await this.extensionsWorkbenchService.uninstall(this.extension);
-			alert(localize('uninstallExtensionComplete', "Please reload Visual Studio Code to complete the uninstallation of the extension {0}.", this.extension.displayName));
+			alert(localize('uninstallExtensionComplete', "Please reload !!APP_NAME!! to complete the uninstallation of the extension {0}.", this.extension.displayName));
 		} catch (error) {
@@ -2597,3 +2597,3 @@ export class ExtensionStatusAction extends ExtensionAction {
 				const link = `[${localize('settings', "settings")}](${URI.parse(`command:workbench.action.openSettings?${encodeURIComponent(JSON.stringify([this.extension.deprecationInfo.settings.map(setting => `@id:${setting}`).join(' ')]))}`)})`;
-				this.updateStatus({ icon: warningIcon, message: new MarkdownString(localize('deprecated with alternate settings tooltip', "This extension is deprecated as this functionality is now built-in to VS Code. Configure these {0} to use this functionality.", link)) }, true);
+				this.updateStatus({ icon: warningIcon, message: new MarkdownString(localize('deprecated with alternate settings tooltip', "This extension is deprecated as this functionality is now built-in to !!APP_NAME!!. Configure these {0} to use this functionality.", link)) }, true);
 			} else {
diff --git a/src/vs/workbench/contrib/extensions/browser/extensionsWorkbenchService.ts b/src/vs/workbench/contrib/extensions/browser/extensionsWorkbenchService.ts
index 5aba80e..e7f02ed 100644
--- a/src/vs/workbench/contrib/extensions/browser/extensionsWorkbenchService.ts
+++ b/src/vs/workbench/contrib/extensions/browser/extensionsWorkbenchService.ts
@@ -468,3 +468,3 @@ export class Extension implements IExtension {
 			return Promise.resolve(`# ${this.displayName || this.name}
-**Notice:** This extension is bundled with Visual Studio Code. It can be disabled but not uninstalled.
+**Notice:** This extension is bundled with !!APP_NAME!!. It can be disabled but not uninstalled.
 ## Features
@@ -506,3 +506,3 @@ ${this.description}
 		if (this.type === ExtensionType.System) {
-			return Promise.resolve(`Please check the [VS Code Release Notes](command:${ShowCurrentReleaseNotesActionId}) for changes to the built-in extensions.`);
+			return Promise.resolve(`Please check the [!!APP_NAME!! Release Notes](command:${ShowCurrentReleaseNotesActionId}) for changes to the built-in extensions.`);
 		}
diff --git a/src/vs/workbench/contrib/extensions/common/extensionsFileTemplate.ts b/src/vs/workbench/contrib/extensions/common/extensionsFileTemplate.ts
index 818e662..2d2ead7 100644
--- a/src/vs/workbench/contrib/extensions/common/extensionsFileTemplate.ts
+++ b/src/vs/workbench/contrib/extensions/common/extensionsFileTemplate.ts
@@ -29,3 +29,3 @@ export const ExtensionsConfigurationSchema: IJSONSchema = {
 			type: 'array',
-			description: localize('app.extensions.json.unwantedRecommendations', "List of extensions recommended by VS Code that should not be recommended for users of this workspace. The identifier of an extension is always '${publisher}.${name}'. For example: 'vscode.csharp'."),
+			description: localize('app.extensions.json.unwantedRecommendations', "List of extensions recommended by !!APP_NAME!! that should not be recommended for users of this workspace. The identifier of an extension is always '${publisher}.${name}'. For example: 'vscode.csharp'."),
 			items: {
@@ -48,3 +48,3 @@ export const ExtensionsConfigurationInitialContent: string = [
 	'\t],',
-	'\t// List of extensions recommended by VS Code that should not be recommended for users of this workspace.',
+	'\t// List of extensions recommended by !!APP_NAME!! that should not be recommended for users of this workspace.',
 	'\t"unwantedRecommendations": [',
diff --git a/src/vs/workbench/contrib/externalUriOpener/common/configuration.ts b/src/vs/workbench/contrib/externalUriOpener/common/configuration.ts
index f54ddfe..946de6b 100644
--- a/src/vs/workbench/contrib/externalUriOpener/common/configuration.ts
+++ b/src/vs/workbench/contrib/externalUriOpener/common/configuration.ts
@@ -57,3 +57,3 @@ export const externalUriOpenersConfigurationNode: IConfigurationNode = {
 						enum: [defaultExternalUriOpenerId],
-						enumDescriptions: [nls.localize('externalUriOpeners.defaultId', "Open using VS Code's standard opener.")],
+						enumDescriptions: [nls.localize('externalUriOpeners.defaultId', "Open using !!APP_NAME!!'s standard opener.")],
 					},
diff --git a/src/vs/workbench/contrib/localization/common/localization.contribution.ts b/src/vs/workbench/contrib/localization/common/localization.contribution.ts
index b89d74b..2a06ffb 100644
--- a/src/vs/workbench/contrib/localization/common/localization.contribution.ts
+++ b/src/vs/workbench/contrib/localization/common/localization.contribution.ts
@@ -58,5 +58,5 @@ export class BaseLocalizationWorkbenchContribution extends Disposable implements
 										type: 'string',
-										description: localize('vscode.extension.contributes.localizations.translations.id', "Id of VS Code or Extension for which this translation is contributed to. Id of VS Code is always `vscode` and of extension should be in format `publisherId.extensionName`."),
+										description: localize('vscode.extension.contributes.localizations.translations.id', "Id of !!APP_NAME!! or Extension for which this translation is contributed to. Id of !!APP_NAME!! is always `vscode` and of extension should be in format `publisherId.extensionName`."),
 										pattern: '^((vscode)|([a-z0-9A-Z][a-z0-9A-Z-]*)\\.([a-z0-9A-Z][a-z0-9A-Z-]*))$',
-										patternErrorMessage: localize('vscode.extension.contributes.localizations.translations.id.pattern', "Id should be `vscode` or in format `publisherId.extensionName` for translating VS code or an extension respectively.")
+										patternErrorMessage: localize('vscode.extension.contributes.localizations.translations.id.pattern', "Id should be `vscode` or in format `publisherId.extensionName` for translating !!APP_NAME!! or an extension respectively.")
 									},
diff --git a/src/vs/workbench/contrib/localization/common/localizationsActions.ts b/src/vs/workbench/contrib/localization/common/localizationsActions.ts
index 8619144..5815a70 100644
--- a/src/vs/workbench/contrib/localization/common/localizationsActions.ts
+++ b/src/vs/workbench/contrib/localization/common/localizationsActions.ts
@@ -26,3 +26,3 @@ export class ConfigureDisplayLanguageAction extends Action2 {
 			metadata: {
-				description: localize2('configureLocaleDescription', "Changes the locale of VS Code based on installed language packs. Common languages include French, Chinese, Spanish, Japanese, German, Korean, and more.")
+				description: localize2('configureLocaleDescription', "Changes the locale of !!APP_NAME!! based on installed language packs. Common languages include French, Chinese, Spanish, Japanese, German, Korean, and more.")
 			}
diff --git a/src/vs/workbench/contrib/preferences/browser/keyboardLayoutPicker.ts b/src/vs/workbench/contrib/preferences/browser/keyboardLayoutPicker.ts
index b533feb..112d695 100644
--- a/src/vs/workbench/contrib/preferences/browser/keyboardLayoutPicker.ts
+++ b/src/vs/workbench/contrib/preferences/browser/keyboardLayoutPicker.ts
@@ -95,4 +95,4 @@ interface IUnknownLayout {
 const DEFAULT_CONTENT: string = [
-	`// ${nls.localize('displayLanguage', 'Defines the keyboard layout used in VS Code in the browser environment.')}`,
-	`// ${nls.localize('doc', 'Open VS Code and run "Developer: Inspect Key Mappings (JSON)" from Command Palette.')}`,
+	`// ${nls.localize('displayLanguage', 'Defines the keyboard layout used in !!APP_NAME!! in the browser environment.')}`,
+	`// ${nls.localize('doc', 'Open !!APP_NAME!! and run "Developer: Inspect Key Mappings (JSON)" from Command Palette.')}`,
 	``,
diff --git a/src/vs/workbench/contrib/remoteTunnel/electron-sandbox/remoteTunnel.contribution.ts b/src/vs/workbench/contrib/remoteTunnel/electron-sandbox/remoteTunnel.contribution.ts
index 5de0485..177e215 100644
--- a/src/vs/workbench/contrib/remoteTunnel/electron-sandbox/remoteTunnel.contribution.ts
+++ b/src/vs/workbench/contrib/remoteTunnel/electron-sandbox/remoteTunnel.contribution.ts
@@ -561,3 +561,3 @@ export class RemoteTunnelWorkbenchContribution extends Disposable implements IWo
 								},
-								"You can now access this machine anywhere via the secure tunnel [{0}](command:{4}). To connect via a different machine, use the generated [{1}]({2}) link or use the [{6}]({7}) extension in the desktop or web. You can [configure](command:{3}) or [turn off](command:{5}) this access via the VS Code Accounts menu.",
+								"You can now access this machine anywhere via the secure tunnel [{0}](command:{4}). To connect via a different machine, use the generated [{1}]({2}) link or use the [{6}]({7}) extension in the desktop or web. You can [configure](command:{3}) or [turn off](command:{5}) this access via the !!APP_NAME!! Accounts menu.",
 								connectionInfo.tunnelName, connectionInfo.domain, linkToOpenForMarkdown, RemoteTunnelCommandIds.manage, RemoteTunnelCommandIds.configure, RemoteTunnelCommandIds.turnOff, remoteExtension.friendlyName, 'https://code.visualstudio.com/docs/remote/tunnels'
diff --git a/src/vs/workbench/contrib/tasks/browser/abstractTaskService.ts b/src/vs/workbench/contrib/tasks/browser/abstractTaskService.ts
index a6295cc..555a37c 100644
--- a/src/vs/workbench/contrib/tasks/browser/abstractTaskService.ts
+++ b/src/vs/workbench/contrib/tasks/browser/abstractTaskService.ts
@@ -3252,3 +3252,3 @@ export abstract class AbstractTaskService extends Disposable implements ITaskSer
 						if (response.code && response.code === TerminateResponseCode.ProcessNotFound) {
-							this._notificationService.error(nls.localize('TerminateAction.noProcess', 'The launched process doesn\'t exist anymore. If the task spawned background tasks exiting VS Code might result in orphaned processes.'));
+							this._notificationService.error(nls.localize('TerminateAction.noProcess', 'The launched process doesn\'t exist anymore. If the task spawned background tasks exiting !!APP_NAME!! might result in orphaned processes.'));
 						} else {
diff --git a/src/vs/workbench/contrib/tasks/common/jsonSchemaCommon.ts b/src/vs/workbench/contrib/tasks/common/jsonSchemaCommon.ts
index 9db6b8a..cecac9f 100644
--- a/src/vs/workbench/contrib/tasks/common/jsonSchemaCommon.ts
+++ b/src/vs/workbench/contrib/tasks/common/jsonSchemaCommon.ts
@@ -193,3 +193,3 @@ const schema: IJSONSchema = {
 					type: 'boolean',
-					description: nls.localize('JsonSchema.tasks.promptOnClose', 'Whether the user is prompted when VS Code closes with a running task.'),
+					description: nls.localize('JsonSchema.tasks.promptOnClose', 'Whether the user is prompted when !!APP_NAME!! closes with a running task.'),
 					default: false
@@ -247,3 +247,3 @@ const schema: IJSONSchema = {
 					type: 'boolean',
-					description: nls.localize('JsonSchema.promptOnClose', 'Whether the user is prompted when VS Code closes with a running background task.'),
+					description: nls.localize('JsonSchema.promptOnClose', 'Whether the user is prompted when !!APP_NAME!! closes with a running background task.'),
 					default: false
diff --git a/src/vs/workbench/contrib/tasks/common/jsonSchema_v2.ts b/src/vs/workbench/contrib/tasks/common/jsonSchema_v2.ts
index e698eb9..b2a6e63 100644
--- a/src/vs/workbench/contrib/tasks/common/jsonSchema_v2.ts
+++ b/src/vs/workbench/contrib/tasks/common/jsonSchema_v2.ts
@@ -424,3 +424,3 @@ const taskConfiguration: IJSONSchema = {
 			type: 'boolean',
-			description: nls.localize('JsonSchema.tasks.promptOnClose', 'Whether the user is prompted when VS Code closes with a running task.'),
+			description: nls.localize('JsonSchema.tasks.promptOnClose', 'Whether the user is prompted when !!APP_NAME!! closes with a running task.'),
 			default: false
diff --git a/src/vs/workbench/contrib/tasks/electron-sandbox/taskService.ts b/src/vs/workbench/contrib/tasks/electron-sandbox/taskService.ts
index 71b4a51..7c6a88f 100644
--- a/src/vs/workbench/contrib/tasks/electron-sandbox/taskService.ts
+++ b/src/vs/workbench/contrib/tasks/electron-sandbox/taskService.ts
@@ -216,3 +216,3 @@ export class TaskService extends AbstractTaskService {
 						return this._dialogService.confirm({
-							message: nls.localize('TaskSystem.noProcess', 'The launched task doesn\'t exist anymore. If the task spawned background processes exiting VS Code might result in orphaned processes. To avoid this start the last background process with a wait flag.'),
+							message: nls.localize('TaskSystem.noProcess', 'The launched task doesn\'t exist anymore. If the task spawned background processes exiting !!APP_NAME!! might result in orphaned processes. To avoid this start the last background process with a wait flag.'),
 							primaryButton: nls.localize({ key: 'TaskSystem.exitAnyways', comment: ['&& denotes a mnemonic'] }, "&&Exit Anyways"),
diff --git a/src/vs/workbench/contrib/terminal/browser/terminalView.ts b/src/vs/workbench/contrib/terminal/browser/terminalView.ts
index 42ff0ac..8f2f57c 100644
--- a/src/vs/workbench/contrib/terminal/browser/terminalView.ts
+++ b/src/vs/workbench/contrib/terminal/browser/terminalView.ts
@@ -213,3 +213,3 @@ export class TerminalViewPane extends ViewPane {
 					}];
-					this._notificationService.prompt(Severity.Warning, nls.localize('terminal.monospaceOnly', "The terminal only supports monospace fonts. Be sure to restart VS Code if this is a newly installed font."), choices);
+					this._notificationService.prompt(Severity.Warning, nls.localize('terminal.monospaceOnly', "The terminal only supports monospace fonts. Be sure to restart !!APP_NAME!! if this is a newly installed font."), choices);
 				}
diff --git a/src/vs/workbench/contrib/terminal/common/terminalConfiguration.ts b/src/vs/workbench/contrib/terminal/common/terminalConfiguration.ts
index dca86e0..3547a08 100644
--- a/src/vs/workbench/contrib/terminal/common/terminalConfiguration.ts
+++ b/src/vs/workbench/contrib/terminal/common/terminalConfiguration.ts
@@ -314,3 +314,3 @@ const terminalConfiguration: IConfigurationNode = {
 		[TerminalSettingId.DetectLocale]: {
-			markdownDescription: localize('terminal.integrated.detectLocale', "Controls whether to detect and set the `$LANG` environment variable to a UTF-8 compliant option since VS Code's terminal only supports UTF-8 encoded data coming from the shell."),
+			markdownDescription: localize('terminal.integrated.detectLocale', "Controls whether to detect and set the `$LANG` environment variable to a UTF-8 compliant option since !!APP_NAME!!'s terminal only supports UTF-8 encoded data coming from the shell."),
 			type: 'string',
@@ -328,3 +328,3 @@ const terminalConfiguration: IConfigurationNode = {
 			markdownEnumDescriptions: [
-				localize('terminal.integrated.gpuAcceleration.auto', "Let VS Code detect which renderer will give the best experience."),
+				localize('terminal.integrated.gpuAcceleration.auto', "Let !!APP_NAME!! detect which renderer will give the best experience."),
 				localize('terminal.integrated.gpuAcceleration.on', "Enable GPU acceleration within the terminal."),
@@ -416,3 +416,3 @@ const terminalConfiguration: IConfigurationNode = {
 				'terminal.integrated.commandsToSkipShell',
-				"A set of command IDs whose keybindings will not be sent to the shell but instead always be handled by VS Code. This allows keybindings that would normally be consumed by the shell to act instead the same as when the terminal is not focused, for example `Ctrl+P` to launch Quick Open.\n\n&nbsp;\n\nMany commands are skipped by default. To override a default and pass that command's keybinding to the shell instead, add the command prefixed with the `-` character. For example add `-workbench.action.quickOpen` to allow `Ctrl+P` to reach the shell.\n\n&nbsp;\n\nThe following list of default skipped commands is truncated when viewed in Settings Editor. To see the full list, {1} and search for the first command from the list below.\n\n&nbsp;\n\nDefault Skipped Commands:\n\n{0}",
+				"A set of command IDs whose keybindings will not be sent to the shell but instead always be handled by !!APP_NAME!!. This allows keybindings that would normally be consumed by the shell to act instead the same as when the terminal is not focused, for example `Ctrl+P` to launch Quick Open.\n\n&nbsp;\n\nMany commands are skipped by default. To override a default and pass that command's keybinding to the shell instead, add the command prefixed with the `-` character. For example add `-workbench.action.quickOpen` to allow `Ctrl+P` to reach the shell.\n\n&nbsp;\n\nThe following list of default skipped commands is truncated when viewed in Settings Editor. To see the full list, {1} and search for the first command from the list below.\n\n&nbsp;\n\nDefault Skipped Commands:\n\n{0}",
 				DEFAULT_COMMANDS_TO_SKIP_SHELL.sort().map(command => `- ${command}`).join('\n'),
@@ -428,3 +428,3 @@ const terminalConfiguration: IConfigurationNode = {
 		[TerminalSettingId.AllowChords]: {
-			markdownDescription: localize('terminal.integrated.allowChords', "Whether or not to allow chord keybindings in the terminal. Note that when this is true and the keystroke results in a chord it will bypass {0}, setting this to false is particularly useful when you want ctrl+k to go to your shell (not VS Code).", '`#terminal.integrated.commandsToSkipShell#`'),
+			markdownDescription: localize('terminal.integrated.allowChords', "Whether or not to allow chord keybindings in the terminal. Note that when this is true and the keystroke results in a chord it will bypass {0}, setting this to false is particularly useful when you want ctrl+k to go to your shell (not !!APP_NAME!!).", '`#terminal.integrated.commandsToSkipShell#`'),
 			type: 'boolean',
@@ -439,3 +439,3 @@ const terminalConfiguration: IConfigurationNode = {
 			restricted: true,
-			markdownDescription: localize('terminal.integrated.env.osx', "Object with environment variables that will be added to the VS Code process to be used by the terminal on macOS. Set to `null` to delete the environment variable."),
+			markdownDescription: localize('terminal.integrated.env.osx', "Object with environment variables that will be added to the !!APP_NAME!! process to be used by the terminal on macOS. Set to `null` to delete the environment variable."),
 			type: 'object',
@@ -448,3 +448,3 @@ const terminalConfiguration: IConfigurationNode = {
 			restricted: true,
-			markdownDescription: localize('terminal.integrated.env.linux', "Object with environment variables that will be added to the VS Code process to be used by the terminal on Linux. Set to `null` to delete the environment variable."),
+			markdownDescription: localize('terminal.integrated.env.linux', "Object with environment variables that will be added to the !!APP_NAME!! process to be used by the terminal on Linux. Set to `null` to delete the environment variable."),
 			type: 'object',
@@ -457,3 +457,3 @@ const terminalConfiguration: IConfigurationNode = {
 			restricted: true,
-			markdownDescription: localize('terminal.integrated.env.windows', "Object with environment variables that will be added to the VS Code process to be used by the terminal on Windows. Set to `null` to delete the environment variable."),
+			markdownDescription: localize('terminal.integrated.env.windows', "Object with environment variables that will be added to the !!APP_NAME!! process to be used by the terminal on Windows. Set to `null` to delete the environment variable."),
 			type: 'object',
@@ -486,3 +486,3 @@ const terminalConfiguration: IConfigurationNode = {
 		[TerminalSettingId.WindowsUseConptyDll]: {
-			markdownDescription: localize('terminal.integrated.windowsUseConptyDll', "Whether to use the experimental conpty.dll (v1.22.250204002) shipped with VS Code, instead of the one bundled with Windows."),
+			markdownDescription: localize('terminal.integrated.windowsUseConptyDll', "Whether to use the experimental conpty.dll (v1.22.250204002) shipped with !!APP_NAME!!, instead of the one bundled with Windows."),
 			type: 'boolean',
@@ -593,3 +593,3 @@ const terminalConfiguration: IConfigurationNode = {
 			restricted: true,
-			markdownDescription: localize('terminal.integrated.shellIntegration.enabled', "Determines whether or not shell integration is auto-injected to support features like enhanced command tracking and current working directory detection. \n\nShell integration works by injecting the shell with a startup script. The script gives VS Code insight into what is happening within the terminal.\n\nSupported shells:\n\n- Linux/macOS: bash, fish, pwsh, zsh\n - Windows: pwsh, git bash\n\nThis setting applies only when terminals are created, so you will need to restart your terminals for it to take effect.\n\n Note that the script injection may not work if you have custom arguments defined in the terminal profile, have enabled {1}, have a [complex bash `PROMPT_COMMAND`](https://code.visualstudio.com/docs/editor/integrated-terminal#_complex-bash-promptcommand), or other unsupported setup. To disable decorations, see {0}", '`#terminal.integrated.shellIntegration.decorationsEnabled#`', '`#editor.accessibilitySupport#`'),
+			markdownDescription: localize('terminal.integrated.shellIntegration.enabled', "Determines whether or not shell integration is auto-injected to support features like enhanced command tracking and current working directory detection. \n\nShell integration works by injecting the shell with a startup script. The script gives !!APP_NAME!! insight into what is happening within the terminal.\n\nSupported shells:\n\n- Linux/macOS: bash, fish, pwsh, zsh\n - Windows: pwsh, git bash\n\nThis setting applies only when terminals are created, so you will need to restart your terminals for it to take effect.\n\n Note that the script injection may not work if you have custom arguments defined in the terminal profile, have enabled {1}, have a [complex bash `PROMPT_COMMAND`](https://code.visualstudio.com/docs/editor/integrated-terminal#_complex-bash-promptcommand), or other unsupported setup. To disable decorations, see {0}", '`#terminal.integrated.shellIntegration.decorationsEnabled#`', '`#editor.accessibilitySupport#`'),
 			type: 'boolean',
diff --git a/src/vs/workbench/contrib/terminalContrib/autoReplies/common/terminalAutoRepliesConfiguration.ts b/src/vs/workbench/contrib/terminalContrib/autoReplies/common/terminalAutoRepliesConfiguration.ts
index 4979520..30ae11b 100644
--- a/src/vs/workbench/contrib/terminalContrib/autoReplies/common/terminalAutoRepliesConfiguration.ts
+++ b/src/vs/workbench/contrib/terminalContrib/autoReplies/common/terminalAutoRepliesConfiguration.ts
@@ -19,3 +19,3 @@ export const terminalAutoRepliesConfiguration: IStringDictionary<IConfigurationP
 	[TerminalAutoRepliesSettingId.AutoReplies]: {
-		markdownDescription: localize('terminal.integrated.autoReplies', "A set of messages that, when encountered in the terminal, will be automatically responded to. Provided the message is specific enough, this can help automate away common responses.\n\nRemarks:\n\n- Use {0} to automatically respond to the terminate batch job prompt on Windows.\n- The message includes escape sequences so the reply might not happen with styled text.\n- Each reply can only happen once every second.\n- Use {1} in the reply to mean the enter key.\n- To unset a default key, set the value to null.\n- Restart VS Code if new don't apply.", '`"Terminate batch job (Y/N)": "Y\\r"`', '`"\\r"`'),
+		markdownDescription: localize('terminal.integrated.autoReplies', "A set of messages that, when encountered in the terminal, will be automatically responded to. Provided the message is specific enough, this can help automate away common responses.\n\nRemarks:\n\n- Use {0} to automatically respond to the terminate batch job prompt on Windows.\n- The message includes escape sequences so the reply might not happen with styled text.\n- Each reply can only happen once every second.\n- Use {1} in the reply to mean the enter key.\n- To unset a default key, set the value to null.\n- Restart !!APP_NAME!! if new don't apply.", '`"Terminate batch job (Y/N)": "Y\\r"`', '`"\\r"`'),
 		type: 'object',
diff --git a/src/vs/workbench/contrib/update/browser/update.ts b/src/vs/workbench/contrib/update/browser/update.ts
index a20d7ae..6861b35 100644
--- a/src/vs/workbench/contrib/update/browser/update.ts
+++ b/src/vs/workbench/contrib/update/browser/update.ts
@@ -583,4 +583,4 @@ export class SwitchProductQualityContribution extends Disposable implements IWor
 							detail: newQuality === 'insider' ?
-								nls.localize('relaunchDetailInsiders', "Press the reload button to switch to the Insiders version of VS Code.") :
-								nls.localize('relaunchDetailStable', "Press the reload button to switch to the Stable version of VS Code."),
+								nls.localize('relaunchDetailInsiders', "Press the reload button to switch to the Insiders version of !!APP_NAME!!.") :
+								nls.localize('relaunchDetailStable', "Press the reload button to switch to the Stable version of !!APP_NAME!!."),
 							primaryButton: nls.localize({ key: 'reload', comment: ['&& denotes a mnemonic'] }, "&&Reload")
@@ -619,3 +619,3 @@ export class SwitchProductQualityContribution extends Disposable implements IWor
 						message: nls.localize('selectSyncService.message', "Choose the settings sync service to use after changing the version"),
-						detail: nls.localize('selectSyncService.detail', "The Insiders version of VS Code will synchronize your settings, keybindings, extensions, snippets and UI State using separate insiders settings sync service by default."),
+						detail: nls.localize('selectSyncService.detail', "The Insiders version of !!APP_NAME!! will synchronize your settings, keybindings, extensions, snippets and UI State using separate insiders settings sync service by default."),
 						buttons: [
diff --git a/src/vs/workbench/contrib/url/browser/trustedDomainsFileSystemProvider.ts b/src/vs/workbench/contrib/url/browser/trustedDomainsFileSystemProvider.ts
index d508a0d..a8f9899 100644
--- a/src/vs/workbench/contrib/url/browser/trustedDomainsFileSystemProvider.ts
+++ b/src/vs/workbench/contrib/url/browser/trustedDomainsFileSystemProvider.ts
@@ -55,3 +55,3 @@ function computeTrustedDomainContent(defaultTrustedDomains: string[], trustedDom
 	if (defaultTrustedDomains.length > 0) {
-		content += `// By default, VS Code trusts "localhost" as well as the following domains:\n`;
+		content += `// By default, !!APP_NAME!! trusts "localhost" as well as the following domains:\n`;
 		defaultTrustedDomains.forEach(d => {
@@ -60,3 +60,3 @@ function computeTrustedDomainContent(defaultTrustedDomains: string[], trustedDom
 	} else {
-		content += `// By default, VS Code trusts "localhost".\n`;
+		content += `// By default, !!APP_NAME!! trusts "localhost".\n`;
 	}
diff --git a/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStarted.contribution.ts b/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStarted.contribution.ts
index 6aa706f..ddbd93e 100644
--- a/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStarted.contribution.ts
+++ b/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStarted.contribution.ts
@@ -51,3 +51,3 @@ registerAction2(class extends Action2 {
 			metadata: {
-				description: localize2('minWelcomeDescription', 'Opens a Walkthrough to help you get started in VS Code.')
+				description: localize2('minWelcomeDescription', 'Opens a Walkthrough to help you get started in !!APP_NAME!!.')
 			}
@@ -379,3 +379,3 @@ configurationRegistry.registerConfiguration({
 				localize({ comment: ['This is the description for a setting. Values surrounded by single quotes are not to be translated.'], key: 'workbench.startupEditor.none' }, "Start without an editor."),
-				localize({ comment: ['This is the description for a setting. Values surrounded by single quotes are not to be translated.'], key: 'workbench.startupEditor.welcomePage' }, "Open the Welcome page, with content to aid in getting started with VS Code and extensions."),
+				localize({ comment: ['This is the description for a setting. Values surrounded by single quotes are not to be translated.'], key: 'workbench.startupEditor.welcomePage' }, "Open the Welcome page, with content to aid in getting started with !!APP_NAME!! and extensions."),
 				localize({ comment: ['This is the description for a setting. Values surrounded by single quotes are not to be translated.'], key: 'workbench.startupEditor.readme' }, "Open the README when opening a folder that contains one, fallback to 'welcomePage' otherwise. Note: This is only observed as a global configuration, it will be ignored if set in a workspace or folder configuration."),
diff --git a/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStarted.ts b/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStarted.ts
index 8d72827..fb31d3a 100644
--- a/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStarted.ts
+++ b/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStarted.ts
@@ -20,3 +20,3 @@ import { splitRecentLabel } from '../../../../base/common/labels.js';
 import { DisposableStore, toDisposable } from '../../../../base/common/lifecycle.js';
-import { ILink, LinkedText, parseLinkedText } from '../../../../base/common/linkedText.js';
+import { ILink, LinkedText } from '../../../../base/common/linkedText.js';
 import { parse } from '../../../../base/common/marshalling.js';
@@ -65,3 +65,3 @@ import { IResolvedWalkthrough, IResolvedWalkthroughStep, IWalkthroughsService, h
 import { RestoreWalkthroughsConfigurationValue, restoreWalkthroughsConfigurationKey } from './startupPage.js';
-import { copilotSettingsMessage, NEW_WELCOME_EXPERIENCE, startEntries } from '../common/gettingStartedContent.js';
+import { NEW_WELCOME_EXPERIENCE, startEntries } from '../common/gettingStartedContent.js';
 import { GroupDirection, GroupsOrder, IEditorGroup, IEditorGroupsService } from '../../../services/editor/common/editorGroupsService.js';
@@ -1618,6 +1618,2 @@ export class GettingStartedPage extends EditorPane {
 					multiStepContainer.appendChild(descElement);
-					const actionMessage = $('span.action-message');
-					const updatedText = parseLinkedText(copilotSettingsMessage);
-					this.buildMarkdownDescription(actionMessage, [updatedText]);
-					multiStepContainer.appendChild(actionMessage);
 				}
diff --git a/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStartedExtensionPoint.ts b/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStartedExtensionPoint.ts
index 3f1c098..26bf670 100644
--- a/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStartedExtensionPoint.ts
+++ b/src/vs/workbench/contrib/welcomeGettingStarted/browser/gettingStartedExtensionPoint.ts
@@ -161,3 +161,3 @@ export const walkthroughsExtensionPoint = ExtensionsRegistry.registerExtensionPo
 											label: 'onCommand',
-											description: localize('walkthroughs.steps.completionEvents.onCommand', 'Check off step when a given command is executed anywhere in VS Code.'),
+											description: localize('walkthroughs.steps.completionEvents.onCommand', 'Check off step when a given command is executed anywhere in !!APP_NAME!!.'),
 											body: 'onCommand:${1:commandId}'
diff --git a/src/vs/workbench/contrib/welcomeGettingStarted/common/gettingStartedContent.ts b/src/vs/workbench/contrib/welcomeGettingStarted/common/gettingStartedContent.ts
index 15600cc..ccd635d 100644
--- a/src/vs/workbench/contrib/welcomeGettingStarted/common/gettingStartedContent.ts
+++ b/src/vs/workbench/contrib/welcomeGettingStarted/common/gettingStartedContent.ts
@@ -15,3 +15,2 @@ import { CONTEXT_ACCESSIBILITY_MODE_ENABLED } from '../../../../platform/accessi
 import { URI } from '../../../../base/common/uri.js';
-import product from '../../../../platform/product/common/product.js';
 
@@ -21,4 +20,2 @@ interface IGettingStartedContentProvider {
 
-export const copilotSettingsMessage = localize({ key: 'settings', comment: ['{Locked="["}', '{Locked="]({0})"}', '{Locked="]({1})"}'] }, "{0} Copilot Free, Pro and Pro+ may show [public code]({1}) suggestions and we may use your data for product improvement. You can change these [settings]({2}) at any time.", product.defaultChatAgent?.providerName, product.defaultChatAgent?.publicCodeMatchesUrl, product.defaultChatAgent?.manageSettingsUrl);
-
 class GettingStartedContentProviderRegistry {
@@ -203,13 +200,2 @@ export const startEntries: GettingStartedStartEntryContent = [
 	},
-	{
-		id: 'topLevelNewWorkspaceChat',
-		title: localize('gettingStarted.newWorkspaceChat.title', "New Workspace with Copilot..."),
-		description: localize('gettingStarted.newWorkspaceChat.description', "Create a new workspace with Copilot"),
-		icon: Codicon.copilot,
-		when: '!isWeb && !chatSetupHidden',
-		content: {
-			type: 'startEntry',
-			command: 'command:welcome.newWorkspaceChat',
-		}
-	},
 ];
@@ -218,24 +204,2 @@ const Button = (title: string, href: string) => `[${title}](${href})`;
 
-const CopilotStepTitle = localize('gettingStarted.copilotSetup.title', "Use AI features with Copilot for free");
-const CopilotDescription = localize({ key: 'gettingStarted.copilotSetup.description', comment: ['{Locked="["}', '{Locked="]({0})"}'] }, "You can use [Copilot]({0}) to generate code across multiple files, fix errors, ask questions about your code and much more using natural language.", product.defaultChatAgent?.documentationUrl ?? '');
-const CopilotSignedOutButton = Button(localize('setupCopilotButton.signIn', "Set up Copilot"), `command:workbench.action.chat.triggerSetup`);
-const CopilotSignedInButton = Button(localize('setupCopilotButton.setup', "Set up Copilot"), `command:workbench.action.chat.triggerSetup`);
-const CopilotCompleteButton = Button(localize('setupCopilotButton.chatWithCopilot', "Chat with Copilot"), 'command:workbench.action.chat.open');
-
-function createCopilotSetupStep(id: string, button: string, when: string, includeTerms: boolean): BuiltinGettingStartedStep {
-	const description = includeTerms ?
-		`${CopilotDescription}\n\n${button}` :
-		`${CopilotDescription}\n${button}`;
-
-	return {
-		id,
-		title: CopilotStepTitle,
-		description,
-		when: `${when} && !chatSetupHidden`,
-		media: {
-			type: 'svg', altText: 'VS Code Copilot multi file edits', path: 'multi-file-edits.svg'
-		},
-	};
-}
-
 export const walkthroughs: GettingStartedWalkthroughContent = [
@@ -243,3 +207,3 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 		id: 'Setup',
-		title: localize('gettingStarted.setup.title', "Get started with VS Code"),
+		title: localize('gettingStarted.setup.title', "Get started with !!APP_NAME!!"),
 		description: localize('gettingStarted.setup.description', "Customize your editor, learn the basics, and start coding"),
@@ -248,3 +212,3 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 		when: '!isWeb',
-		walkthroughPageTitle: localize('gettingStarted.setup.walkthroughPageTitle', 'Setup VS Code'),
+		walkthroughPageTitle: localize('gettingStarted.setup.walkthroughPageTitle', 'Setup !!APP_NAME!!'),
 		next: 'Beginner',
@@ -253,5 +217,2 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 			steps: [
-				createCopilotSetupStep('CopilotSetupSignedOut', CopilotSignedOutButton, 'chatEntitlementSignedOut', true),
-				createCopilotSetupStep('CopilotSetupComplete', CopilotCompleteButton, 'chatSetupInstalled && !chatSetupDisabled && (chatPlanPro || chatPlanProPlus || chatPlanBusiness || chatPlanEnterprise || chatPlanFree)', false),
-				createCopilotSetupStep('CopilotSetupSignedIn', CopilotSignedInButton, '!chatEntitlementSignedOut && (!chatSetupInstalled || chatSetupDisabled || chatPlanCanSignUp)', true),
 				{
@@ -269,6 +230,6 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 					title: localize('gettingStarted.extensions.title', "Code with extensions"),
-					description: localize('gettingStarted.extensionsWeb.description.interpolated', "Extensions are VS Code's power-ups. A growing number are becoming available in the web.\n{0}", Button(localize('browsePopularWeb', "Browse Popular Web Extensions"), 'command:workbench.extensions.action.showPopularExtensions')),
+					description: localize('gettingStarted.extensionsWeb.description.interpolated', "Extensions are !!APP_NAME!!'s power-ups. A growing number are becoming available in the web.\n{0}", Button(localize('browsePopularWeb', "Browse Popular Web Extensions"), 'command:workbench.extensions.action.showPopularExtensions')),
 					when: 'workspacePlatform == \'webworker\'',
 					media: {
-						type: 'svg', altText: 'VS Code extension marketplace with featured language extensions', path: 'extensions-web.svg'
+						type: 'svg', altText: '!!APP_NAME!! extension marketplace with featured language extensions', path: 'extensions-web.svg'
 					},
@@ -284,21 +245,2 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 				},
-				// Hidden in favor of copilot entry (to be revisited when copilot entry moves, if at all)
-				// {
-				// 	id: 'settings',
-				// 	title: localize('gettingStarted.settings.title', "Tune your settings"),
-				// 	description: localize('gettingStarted.settings.description.interpolated', "Customize every aspect of VS Code and your extensions to your liking. Commonly used settings are listed first to get you started.\n{0}", Button(localize('tweakSettings', "Open Settings"), 'command:toSide:workbench.action.openSettings')),
-				// 	media: {
-				// 		type: 'svg', altText: 'VS Code Settings', path: 'settings.svg'
-				// 	},
-				// },
-				// {
-				// 	id: 'settingsSync',
-				// 	title: localize('gettingStarted.settingsSync.title', "Sync settings across devices"),
-				// 	description: localize('gettingStarted.settingsSync.description.interpolated', "Keep your essential customizations backed up and updated across all your devices.\n{0}", Button(localize('enableSync', "Backup and Sync Settings"), 'command:workbench.userDataSync.actions.turnOn')),
-				// 	when: 'syncStatus != uninitialized',
-				// 	completionEvents: ['onEvent:sync-enabled'],
-				// 	media: {
-				// 		type: 'svg', altText: 'The "Turn on Sync" entry in the settings gear menu.', path: 'settingsSync.svg'
-				// 	},
-				// },
 				{
@@ -306,3 +248,3 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 					title: localize('gettingStarted.settings.title', "Tune your settings"),
-					description: localize('gettingStarted.settingsAndSync.description.interpolated', "Customize every aspect of VS Code and your extensions to your liking. [Back up and sync](command:workbench.userDataSync.actions.turnOn) your essential customizations across all your devices.\n{0}", Button(localize('tweakSettings', "Open Settings"), 'command:toSide:workbench.action.openSettings')),
+					description: localize('gettingStarted.settingsAndSync.description.interpolated', "Customize every aspect of !!APP_NAME!! and your extensions to your liking. [Back up and sync](command:workbench.userDataSync.actions.turnOn) your essential customizations across all your devices.\n{0}", Button(localize('tweakSettings', "Open Settings"), 'command:toSide:workbench.action.openSettings')),
 					when: 'syncStatus != uninitialized',
@@ -310,3 +252,3 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 					media: {
-						type: 'svg', altText: 'VS Code Settings', path: 'settings.svg'
+						type: 'svg', altText: '!!APP_NAME!! Settings', path: 'settings.svg'
 					},
@@ -316,24 +258,5 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 					title: localize('gettingStarted.commandPalette.title', "Unlock productivity with the Command Palette "),
-					description: localize('gettingStarted.commandPalette.description.interpolated', "Run commands without reaching for your mouse to accomplish any task in VS Code.\n{0}", Button(localize('commandPalette', "Open Command Palette"), 'command:workbench.action.showCommands')),
+					description: localize('gettingStarted.commandPalette.description.interpolated', "Run commands without reaching for your mouse to accomplish any task in !!APP_NAME!!.\n{0}", Button(localize('commandPalette', "Open Command Palette"), 'command:workbench.action.showCommands')),
 					media: { type: 'svg', altText: 'Command Palette overlay for searching and executing commands.', path: 'commandPalette.svg' },
 				},
-				// Hidden in favor of copilot entry (to be revisited when copilot entry moves, if at all)
-				// {
-				// 	id: 'pickAFolderTask-Mac',
-				// 	title: localize('gettingStarted.setup.OpenFolder.title', "Open up your code"),
-				// 	description: localize('gettingStarted.setup.OpenFolder.description.interpolated', "You're all set to start coding. Open a project folder to get your files into VS Code.\n{0}", Button(localize('pickFolder', "Pick a Folder"), 'command:workbench.action.files.openFileFolder')),
-				// 	when: 'isMac && workspaceFolderCount == 0',
-				// 	media: {
-				// 		type: 'svg', altText: 'Explorer view showing buttons for opening folder and cloning repository.', path: 'openFolder.svg'
-				// 	}
-				// },
-				// {
-				// 	id: 'pickAFolderTask-Other',
-				// 	title: localize('gettingStarted.setup.OpenFolder.title', "Open up your code"),
-				// 	description: localize('gettingStarted.setup.OpenFolder.description.interpolated', "You're all set to start coding. Open a project folder to get your files into VS Code.\n{0}", Button(localize('pickFolder', "Pick a Folder"), 'command:workbench.action.files.openFolder')),
-				// 	when: '!isMac && workspaceFolderCount == 0',
-				// 	media: {
-				// 		type: 'svg', altText: 'Explorer view showing buttons for opening folder and cloning repository.', path: 'openFolder.svg'
-				// 	}
-				// },
 				{
@@ -350,4 +273,4 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 					title: localize('gettingStarted.videoTutorial.title', "Watch video tutorials"),
-					description: localize('gettingStarted.videoTutorial.description.interpolated', "Watch the first in a series of short & practical video tutorials for VS Code's key features.\n{0}", Button(localize('watch', "Watch Tutorial"), 'https://aka.ms/vscode-getting-started-video')),
-					media: { type: 'svg', altText: 'VS Code Settings', path: 'learn.svg' },
+					description: localize('gettingStarted.videoTutorial.description.interpolated', "Watch the first in a series of short & practical video tutorials for !!APP_NAME!!'s key features.\n{0}", Button(localize('watch', "Watch Tutorial"), 'https://aka.ms/vscode-getting-started-video')),
+					media: { type: 'svg', altText: '!!APP_NAME!! Settings', path: 'learn.svg' },
 				}
@@ -359,3 +282,3 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 		id: 'SetupWeb',
-		title: localize('gettingStarted.setupWeb.title', "Get Started with VS Code for the Web"),
+		title: localize('gettingStarted.setupWeb.title', "Get Started with !!APP_NAME!! for the Web"),
 		description: localize('gettingStarted.setupWeb.description', "Customize your editor, learn the basics, and start coding"),
@@ -365,3 +288,3 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 		next: 'Beginner',
-		walkthroughPageTitle: localize('gettingStarted.setupWeb.walkthroughPageTitle', 'Setup VS Code Web'),
+		walkthroughPageTitle: localize('gettingStarted.setupWeb.walkthroughPageTitle', 'Setup !!APP_NAME!! Web'),
 		content: {
@@ -391,6 +314,6 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 					title: localize('gettingStarted.extensions.title', "Code with extensions"),
-					description: localize('gettingStarted.extensionsWeb.description.interpolated', "Extensions are VS Code's power-ups. A growing number are becoming available in the web.\n{0}", Button(localize('browsePopularWeb', "Browse Popular Web Extensions"), 'command:workbench.extensions.action.showPopularExtensions')),
+					description: localize('gettingStarted.extensionsWeb.description.interpolated', "Extensions are !!APP_NAME!!'s power-ups. A growing number are becoming available in the web.\n{0}", Button(localize('browsePopularWeb', "Browse Popular Web Extensions"), 'command:workbench.extensions.action.showPopularExtensions')),
 					when: 'workspacePlatform == \'webworker\'',
 					media: {
-						type: 'svg', altText: 'VS Code extension marketplace with featured language extensions', path: 'extensions-web.svg'
+						type: 'svg', altText: '!!APP_NAME!! extension marketplace with featured language extensions', path: 'extensions-web.svg'
 					},
@@ -406,12 +329,2 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 				},
-				{
-					id: 'settingsSyncWeb',
-					title: localize('gettingStarted.settingsSync.title', "Sync settings across devices"),
-					description: localize('gettingStarted.settingsSync.description.interpolated', "Keep your essential customizations backed up and updated across all your devices.\n{0}", Button(localize('enableSync', "Backup and Sync Settings"), 'command:workbench.userDataSync.actions.turnOn')),
-					when: 'syncStatus != uninitialized',
-					completionEvents: ['onEvent:sync-enabled'],
-					media: {
-						type: 'svg', altText: 'The "Turn on Sync" entry in the settings gear menu.', path: 'settingsSync.svg'
-					},
-				},
 				{
@@ -419,3 +332,3 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 					title: localize('gettingStarted.commandPalette.title', "Unlock productivity with the Command Palette "),
-					description: localize('gettingStarted.commandPalette.description.interpolated', "Run commands without reaching for your mouse to accomplish any task in VS Code.\n{0}", Button(localize('commandPalette', "Open Command Palette"), 'command:workbench.action.showCommands')),
+					description: localize('gettingStarted.commandPalette.description.interpolated', "Run commands without reaching for your mouse to accomplish any task in !!APP_NAME!!.\n{0}", Button(localize('commandPalette', "Open Command Palette"), 'command:workbench.action.showCommands')),
 					media: { type: 'svg', altText: 'Command Palette overlay for searching and executing commands.', path: 'commandPalette.svg' },
@@ -425,3 +338,3 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 					title: localize('gettingStarted.setup.OpenFolder.title', "Open up your code"),
-					description: localize('gettingStarted.setup.OpenFolderWeb.description.interpolated', "You're all set to start coding. You can open a local project or a remote repository to get your files into VS Code.\n{0}\n{1}", Button(localize('openFolder', "Open Folder"), 'command:workbench.action.addRootFolder'), Button(localize('openRepository', "Open Repository"), 'command:remoteHub.openRepository')),
+					description: localize('gettingStarted.setup.OpenFolderWeb.description.interpolated', "You're all set to start coding. You can open a local project or a remote repository to get your files into !!APP_NAME!!.\n{0}\n{1}", Button(localize('openFolder', "Open Folder"), 'command:workbench.action.addRootFolder'), Button(localize('openRepository', "Open Repository"), 'command:remoteHub.openRepository')),
 					when: 'workspaceFolderCount == 0',
@@ -446,3 +359,3 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 		title: localize('gettingStarted.setupAccessibility.title', "Get Started with Accessibility Features"),
-		description: localize('gettingStarted.setupAccessibility.description', "Learn the tools and shortcuts that make VS Code accessible. Note that some actions are not actionable from within the context of the walkthrough."),
+		description: localize('gettingStarted.setupAccessibility.description', "Learn the tools and shortcuts that make !!APP_NAME!! accessible. Note that some actions are not actionable from within the context of the walkthrough."),
 		isFeatured: true,
@@ -451,3 +364,3 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 		next: 'Setup',
-		walkthroughPageTitle: localize('gettingStarted.setupAccessibility.walkthroughPageTitle', 'Setup VS Code Accessibility'),
+		walkthroughPageTitle: localize('gettingStarted.setupAccessibility.walkthroughPageTitle', 'Setup !!APP_NAME!! Accessibility'),
 		content: {
@@ -482,3 +395,3 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 					title: localize('gettingStarted.commandPaletteAccessibility.title', "Unlock productivity with the Command Palette "),
-					description: localize('gettingStarted.commandPaletteAccessibility.description.interpolated', "Run commands without reaching for your mouse to accomplish any task in VS Code.\n{0}", Button(localize('commandPalette', "Open Command Palette"), 'command:workbench.action.showCommands')),
+					description: localize('gettingStarted.commandPaletteAccessibility.description.interpolated', "Run commands without reaching for your mouse to accomplish any task in !!APP_NAME!!.\n{0}", Button(localize('commandPalette', "Open Command Palette"), 'command:workbench.action.showCommands')),
 					media: { type: 'markdown', path: 'empty' },
@@ -555,6 +468,6 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 					title: localize('gettingStarted.extensions.title', "Code with extensions"),
-					description: localize('gettingStarted.extensions.description.interpolated', "Extensions are VS Code's power-ups. They range from handy productivity hacks, expanding out-of-the-box features, to adding completely new capabilities.\n{0}", Button(localize('browsePopular', "Browse Popular Extensions"), 'command:workbench.extensions.action.showPopularExtensions')),
+					description: localize('gettingStarted.extensions.description.interpolated', "Extensions are !!APP_NAME!!'s power-ups. They range from handy productivity hacks, expanding out-of-the-box features, to adding completely new capabilities.\n{0}", Button(localize('browsePopular', "Browse Popular Extensions"), 'command:workbench.extensions.action.showPopularExtensions')),
 					when: 'workspacePlatform != \'webworker\'',
 					media: {
-						type: 'svg', altText: 'VS Code extension marketplace with featured language extensions', path: 'extensions.svg'
+						type: 'svg', altText: '!!APP_NAME!! extension marketplace with featured language extensions', path: 'extensions.svg'
 					},
@@ -672,75 +585,2 @@ export const walkthroughs: GettingStartedWalkthroughContent = [
 	},
-	{
-		id: `${NEW_WELCOME_EXPERIENCE}`,
-		title: localize('gettingStarted.new.title', "Get started with VS Code"),
-		description: localize('gettingStarted.new.description', "Supercharge coding with AI"),
-		isFeatured: false,
-		icon: setupIcon,
-		when: '!isWeb',
-		walkthroughPageTitle: localize('gettingStarted.new.walkthroughPageTitle', 'Set up VS Code'),
-		content: {
-			type: 'steps',
-			steps: [
-				{
-					id: 'copilotSetup.chat',
-					title: localize('gettingStarted.agentMode.title', "Agent mode"),
-					description: localize('gettingStarted.agentMode.description', "Analyzes the problem, plans next steps, and makes changes for you."),
-					media: {
-						type: 'svg', altText: 'VS Code Copilot multi file edits', path: 'multi-file-edits.svg'
-					},
-				},
-				{
-					id: 'copilotSetup.inline',
-					title: localize('gettingStarted.nes.title', "Next edit suggestions"),
-					description: localize('gettingStarted.nes.description', "Get code suggestions that predict your next edit."),
-					media: {
-						type: 'svg', altText: 'Next edit suggestions', path: 'ai-powered-suggestions.svg'
-					},
-				},
-				{
-					id: 'copilotSetup.customize',
-					title: localize('gettingStarted.customize.title', "Personalized to how you work"),
-					description: localize('gettingStarted.customize.description', "Swap models, add agent mode tools, and create personalized instructions.\n{0}", Button(localize('signUp', "Set up AI"), 'command:workbench.action.chat.triggerSetupWithoutDialog')),
-					media: {
-						type: 'svg', altText: 'Personalize', path: 'customize-ai.svg'
-					},
-				},
-				{
-					id: 'newCommandPaletteTask',
-					title: localize('newgettingStarted.commandPalette.title', "All commands within reach"),
-					description: localize('gettingStarted.commandPalette.description.interpolated', "Run commands without reaching for your mouse to accomplish any task in VS Code.\n{0}", Button(localize('commandPalette', "Open Command Palette"), 'command:workbench.action.showCommands')),
-					media: { type: 'svg', altText: 'Command Palette overlay for searching and executing commands.', path: 'commandPalette.svg' },
-				},
-				{
-					id: 'newPickColorTheme',
-					title: localize('gettingStarted.pickColor.title', "Choose your theme"),
-					description: localize('gettingStarted.pickColor.description.interpolated', "The right theme helps you focus on your code, is easy on your eyes, and is simply more fun to use.\n{0}", Button(localize('titleID', "Browse Color Themes"), 'command:workbench.action.selectTheme')),
-					completionEvents: [
-						'onSettingChanged:workbench.colorTheme',
-						'onCommand:workbench.action.selectTheme'
-					],
-					media: { type: 'markdown', path: 'theme_picker_small', }
-				},
-				{
-					id: 'newFindLanguageExtensions',
-					title: localize('newgettingStarted.findLanguageExts.title', "Support for all languages"),
-					description: localize('newgettingStarted.findLanguageExts.description.interpolated', "Install the language extensions you need in your toolkit.\n{0}", Button(localize('browseLangExts', "Browse Language Extensions"), 'command:workbench.extensions.action.showLanguageExtensions')),
-					when: 'workspacePlatform != \'webworker\'',
-					media: {
-						type: 'svg', altText: 'Language extensions', path: 'languages.svg'
-					},
-				},
-				{
-					id: 'newSettingsAndSync',
-					title: localize('newgettingStarted.settings.title', "Customize every aspect of VS Code"),
-					description: localize('newgettingStarted.settingsAndSync.description.interpolated', "[Back up and sync](command:workbench.userDataSync.actions.turnOn) settings across all your devices.\n{0}", Button(localize('tweakSettings', "Open Settings"), 'command:toSide:workbench.action.openSettings')),
-					when: 'syncStatus != uninitialized',
-					completionEvents: ['onEvent:sync-enabled'],
-					media: {
-						type: 'svg', altText: 'VS Code Settings', path: 'settings.svg'
-					},
-				},
-			]
-		}
-	}
 ];
diff --git a/src/vs/workbench/contrib/welcomeWalkthrough/browser/editor/vs_code_editor_walkthrough.ts b/src/vs/workbench/contrib/welcomeWalkthrough/browser/editor/vs_code_editor_walkthrough.ts
index bdd30bf..317d11c 100644
--- a/src/vs/workbench/contrib/welcomeWalkthrough/browser/editor/vs_code_editor_walkthrough.ts
+++ b/src/vs/workbench/contrib/welcomeWalkthrough/browser/editor/vs_code_editor_walkthrough.ts
@@ -13,3 +13,3 @@ export default function content(accessor: ServicesAccessor) {
 ## Interactive Editor Playground
-The core editor in VS Code is packed with features.  This page highlights a number of them and lets you interactively try them out through the use of a number of embedded editors.  For full details on the editor features for VS Code and more head over to our [documentation](https://code.visualstudio.com/docs).
+The core editor in !!APP_NAME!! is packed with features.  This page highlights a number of them and lets you interactively try them out through the use of a number of embedded editors.  For full details on the editor features for !!APP_NAME!! and more head over to our [documentation](https://code.visualstudio.com/docs).
 
@@ -46,3 +46,3 @@ That is the tip of the iceberg for multi-cursor editing. Have a look at the sele
 
-Visual Studio Code comes with the powerful IntelliSense for JavaScript and TypeScript pre-installed. In the below example, position the text cursor right after the dot and press kb(editor.action.triggerSuggest) to invoke IntelliSense.  Notice how the suggestions come from the Canvas API.
+!!APP_NAME!! comes with the powerful IntelliSense for JavaScript and TypeScript pre-installed. In the below example, position the text cursor right after the dot and press kb(editor.action.triggerSuggest) to invoke IntelliSense.  Notice how the suggestions come from the Canvas API.
 
@@ -97,3 +97,3 @@ function Book(title, author) {
 
-> **JSDoc Tip:** VS Code's IntelliSense uses JSDoc comments to provide richer suggestions. The types and documentation from JSDoc comments show up when you hover over a reference to |Book| or in IntelliSense when you create a new instance of |Book|.
+> **JSDoc Tip:** !!APP_NAME!!'s IntelliSense uses JSDoc comments to provide richer suggestions. The types and documentation from JSDoc comments show up when you hover over a reference to |Book| or in IntelliSense when you create a new instance of |Book|.
 
@@ -183,3 +183,3 @@ easy = 42;
 ## Thanks!
-Well if you have got this far then you will have touched on some of the editing features in Visual Studio Code.  But don't stop now :)  We have lots of additional [documentation](https://code.visualstudio.com/docs), [introductory videos](https://code.visualstudio.com/docs/getstarted/introvideos) and [tips and tricks](https://go.microsoft.com/fwlink/?linkid=852118) for the product that will help you learn how to use it.  And while you are here, here are a few additional things you can try:
+Well if you have got this far then you will have touched on some of the editing features in !!APP_NAME!!.  But don't stop now :)  We have lots of additional [documentation](https://code.visualstudio.com/docs), [introductory videos](https://code.visualstudio.com/docs/getstarted/introvideos) and [tips and tricks](https://go.microsoft.com/fwlink/?linkid=852118) for the product that will help you learn how to use it.  And while you are here, here are a few additional things you can try:
 - Open the Integrated Terminal by pressing kb(workbench.action.terminal.toggleTerminal), then see what's possible by [reviewing the terminal documentation](https://code.visualstudio.com/docs/editor/integrated-terminal)
diff --git a/src/vs/workbench/contrib/workspace/browser/workspace.contribution.ts b/src/vs/workbench/contrib/workspace/browser/workspace.contribution.ts
index c512b64..dfa2150 100644
--- a/src/vs/workbench/contrib/workspace/browser/workspace.contribution.ts
+++ b/src/vs/workbench/contrib/workspace/browser/workspace.contribution.ts
@@ -733,3 +733,3 @@ Registry.as<IConfigurationRegistry>(ConfigurationExtensions.Configuration)
 				default: true,
-				description: localize('workspace.trust.description', "Controls whether or not Workspace Trust is enabled within VS Code."),
+				description: localize('workspace.trust.description', "Controls whether or not Workspace Trust is enabled within !!APP_NAME!!."),
 				tags: [WORKSPACE_TRUST_SETTING_TAG],
@@ -779,3 +779,3 @@ Registry.as<IConfigurationRegistry>(ConfigurationExtensions.Configuration)
 				default: true,
-				markdownDescription: localize('workspace.trust.emptyWindow.description', "Controls whether or not the empty window is trusted by default within VS Code. When used with `#{0}#`, you can enable the full functionality of VS Code without prompting in an empty window.", WORKSPACE_TRUST_UNTRUSTED_FILES),
+				markdownDescription: localize('workspace.trust.emptyWindow.description', "Controls whether or not the empty window is trusted by default within !!APP_NAME!!. When used with `#{0}#`, you can enable the full functionality of !!APP_NAME!! without prompting in an empty window.", WORKSPACE_TRUST_UNTRUSTED_FILES),
 				tags: [WORKSPACE_TRUST_SETTING_TAG],
diff --git a/src/vs/workbench/electron-sandbox/desktop.contribution.ts b/src/vs/workbench/electron-sandbox/desktop.contribution.ts
index 4dc6cfe..71e0bb1 100644
--- a/src/vs/workbench/electron-sandbox/desktop.contribution.ts
+++ b/src/vs/workbench/electron-sandbox/desktop.contribution.ts
@@ -384,3 +384,3 @@ import { registerWorkbenchContribution2, WorkbenchPhase } from '../common/contri
 		allowTrailingCommas: true,
-		description: 'VSCode static command line definition file',
+		description: '!!APP_NAME!! static command line definition file',
 		type: 'object',
@@ -429,3 +429,3 @@ import { registerWorkbenchContribution2, WorkbenchPhase } from '../common/contri
 				type: 'boolean',
-				description: localize('argv.disableChromiumSandbox', "Disables the Chromium sandbox. This is useful when running VS Code as elevated on Linux and running under Applocker on Windows.")
+				description: localize('argv.disableChromiumSandbox', "Disables the Chromium sandbox. This is useful when running !!APP_NAME!! as elevated on Linux and running under Applocker on Windows.")
 			},
@@ -433,3 +433,3 @@ import { registerWorkbenchContribution2, WorkbenchPhase } from '../common/contri
 				type: 'boolean',
-				description: localize('argv.useInMemorySecretStorage', "Ensures that an in-memory store will be used for secret storage instead of using the OS's credential store. This is often used when running VS Code extension tests or when you're experiencing difficulties with the credential store.")
+				description: localize('argv.useInMemorySecretStorage', "Ensures that an in-memory store will be used for secret storage instead of using the OS's credential store. This is often used when running !!APP_NAME!! extension tests or when you're experiencing difficulties with the credential store.")
 			}
diff --git a/src/vs/workbench/services/extensionManagement/common/extensionManagementService.ts b/src/vs/workbench/services/extensionManagement/common/extensionManagementService.ts
index e6805ca..f61cecc 100644
--- a/src/vs/workbench/services/extensionManagement/common/extensionManagementService.ts
+++ b/src/vs/workbench/services/extensionManagement/common/extensionManagementService.ts
@@ -1044,3 +1044,3 @@ export class ExtensionManagementService extends CommontExtensionManagementServic
 
-		const productName = localize('VS Code for Web', "{0} for the Web", this.productService.nameLong);
+		const productName = localize('!!APP_NAME!! for Web', "{0} for the Web", this.productService.nameLong);
 		const virtualWorkspaceSupport = this.extensionManifestPropertiesService.getExtensionVirtualWorkspaceSupportType(manifest);
diff --git a/src/vs/workbench/services/extensions/common/extensionsRegistry.ts b/src/vs/workbench/services/extensions/common/extensionsRegistry.ts
index 859b976..06386e0 100644
--- a/src/vs/workbench/services/extensions/common/extensionsRegistry.ts
+++ b/src/vs/workbench/services/extensions/common/extensionsRegistry.ts
@@ -181,3 +181,3 @@ export const schema: IJSONSchema = {
 					type: 'string',
-					description: nls.localize('vscode.extension.engines.vscode', 'For VS Code extensions, specifies the VS Code version that the extension is compatible with. Cannot be *. For example: ^0.10.5 indicates compatibility with a minimum VS Code version of 0.10.5.'),
+					description: nls.localize('vscode.extension.engines.vscode', 'For !!APP_NAME!! extensions, specifies the !!APP_NAME!! version that the extension is compatible with. Cannot be *. For example: ^0.10.5 indicates compatibility with a minimum !!APP_NAME!! version of 0.10.5.'),
 					default: '^1.22.0',
@@ -187,3 +187,3 @@ export const schema: IJSONSchema = {
 		publisher: {
-			description: nls.localize('vscode.extension.publisher', 'The publisher of the VS Code extension.'),
+			description: nls.localize('vscode.extension.publisher', 'The publisher of the !!APP_NAME!! extension.'),
 			type: 'string'
@@ -191,3 +191,3 @@ export const schema: IJSONSchema = {
 		displayName: {
-			description: nls.localize('vscode.extension.displayName', 'The display name for the extension used in the VS Code gallery.'),
+			description: nls.localize('vscode.extension.displayName', 'The display name for the extension used in the !!APP_NAME!! gallery.'),
 			type: 'string'
@@ -195,3 +195,3 @@ export const schema: IJSONSchema = {
 		categories: {
-			description: nls.localize('vscode.extension.categories', 'The categories used by the VS Code gallery to categorize the extension.'),
+			description: nls.localize('vscode.extension.categories', 'The categories used by the !!APP_NAME!! gallery to categorize the extension.'),
 			type: 'array',
@@ -212,6 +212,6 @@ export const schema: IJSONSchema = {
 			type: 'object',
-			description: nls.localize('vscode.extension.galleryBanner', 'Banner used in the VS Code marketplace.'),
+			description: nls.localize('vscode.extension.galleryBanner', 'Banner used in the !!APP_NAME!! marketplace.'),
 			properties: {
 				color: {
-					description: nls.localize('vscode.extension.galleryBanner.color', 'The banner color on the VS Code marketplace page header.'),
+					description: nls.localize('vscode.extension.galleryBanner.color', 'The banner color on the !!APP_NAME!! marketplace page header.'),
 					type: 'string'
@@ -226,3 +226,3 @@ export const schema: IJSONSchema = {
 		contributes: {
-			description: nls.localize('vscode.extension.contributes', 'All contributions of the VS Code extension represented by this package.'),
+			description: nls.localize('vscode.extension.contributes', 'All contributions of the !!APP_NAME!! extension represented by this package.'),
 			type: 'object',
@@ -260,3 +260,3 @@ export const schema: IJSONSchema = {
 		activationEvents: {
-			description: nls.localize('vscode.extension.activationEvents', 'Activation events for the VS Code extension.'),
+			description: nls.localize('vscode.extension.activationEvents', 'Activation events for the !!APP_NAME!! extension.'),
 			type: 'array',
@@ -412,3 +412,3 @@ export const schema: IJSONSchema = {
 						label: '*',
-						description: nls.localize('vscode.extension.activationEvents.star', 'An activation event emitted on VS Code startup. To ensure a great end user experience, please use this activation event in your extension only when no other activation events combination works in your use-case.'),
+						description: nls.localize('vscode.extension.activationEvents.star', 'An activation event emitted on !!APP_NAME!! startup. To ensure a great end user experience, please use this activation event in your extension only when no other activation events combination works in your use-case.'),
 						body: '*'
@@ -584,3 +584,3 @@ export const schema: IJSONSchema = {
 				'vscode:prepublish': {
-					description: nls.localize('vscode.extension.scripts.prepublish', 'Script executed before the package is published as a VS Code extension.'),
+					description: nls.localize('vscode.extension.scripts.prepublish', 'Script executed before the package is published as a !!APP_NAME!! extension.'),
 					type: 'string'
@@ -588,3 +588,3 @@ export const schema: IJSONSchema = {
 				'vscode:uninstall': {
-					description: nls.localize('vscode.extension.scripts.uninstall', 'Uninstall hook for VS Code extension. Script that gets executed when the extension is completely uninstalled from VS Code which is when VS Code is restarted (shutdown and start) after the extension is uninstalled. Only Node scripts are supported.'),
+					description: nls.localize('vscode.extension.scripts.uninstall', 'Uninstall hook for !!APP_NAME!! extension. Script that gets executed when the extension is completely uninstalled from !!APP_NAME!! which is when !!APP_NAME!! is restarted (shutdown and start) after the extension is uninstalled. Only Node scripts are supported.'),
 					type: 'string'
diff --git a/src/vs/workbench/services/extensions/electron-sandbox/nativeExtensionService.ts b/src/vs/workbench/services/extensions/electron-sandbox/nativeExtensionService.ts
index 0506fdc..cb3c491 100644
--- a/src/vs/workbench/services/extensions/electron-sandbox/nativeExtensionService.ts
+++ b/src/vs/workbench/services/extensions/electron-sandbox/nativeExtensionService.ts
@@ -167,3 +167,3 @@ export class NativeExtensionService extends AbstractExtensionService implements
 					[{
-						label: nls.localize('relaunch', "Relaunch VS Code"),
+						label: nls.localize('relaunch', "Relaunch !!APP_NAME!!"),
 						run: () => {
diff --git a/src/vs/workbench/services/userDataProfile/browser/userDataProfileManagement.ts b/src/vs/workbench/services/userDataProfile/browser/userDataProfileManagement.ts
index cc56d9a..f446b8e 100644
--- a/src/vs/workbench/services/userDataProfile/browser/userDataProfileManagement.ts
+++ b/src/vs/workbench/services/userDataProfile/browser/userDataProfileManagement.ts
@@ -199,3 +199,3 @@ export class UserDataProfileManagementService extends Disposable implements IUse
 				const { confirmed } = await this.dialogService.confirm({
-					message: reloadMessage ?? localize('reload message', "Switching a profile requires reloading VS Code."),
+					message: reloadMessage ?? localize('reload message', "Switching a profile requires reloading !!APP_NAME!!."),
 					primaryButton: localize('reload button', "&&Reload"),
