diff --git a/src/vs/platform/extensionManagement/node/extensionManagementService.ts b/src/vs/platform/extensionManagement/node/extensionManagementService.ts
index 0a50a2e..5ee6782 100644
--- a/src/vs/platform/extensionManagement/node/extensionManagementService.ts
+++ b/src/vs/platform/extensionManagement/node/extensionManagementService.ts
@@ -36,3 +36,2 @@ import {
 	IAllowedExtensionsService,
-	VerifyExtensionSignatureConfigKey,
 	shouldRequireRepositorySignatureFor,
@@ -89,2 +88,3 @@ export class ExtensionManagementService extends AbstractExtensionManagementServi
 		@IFileService private readonly fileService: IFileService,
+		// @ts-expect-error no-unused-variable
 		@IConfigurationService private readonly configurationService: IConfigurationService,
@@ -333,4 +333,3 @@ export class ExtensionManagementService extends AbstractExtensionManagementServi
 		if (verifySignature) {
-			const value = this.configurationService.getValue(VerifyExtensionSignatureConfigKey);
-			verifySignature = isBoolean(value) ? value : true;
+			verifySignature = false;
 		}
