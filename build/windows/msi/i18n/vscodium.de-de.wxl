﻿<?xml version="1.0" encoding="utf-8"?>
<WixLocalization Culture="de-de" Codepage="1252" xmlns="http://schemas.microsoft.com/wix/2006/localization">
  <String Id="ProductLanguage">1031</String>
  <String Id="ProductName">@@PRODUCT_NAME@@</String>
  <String Id="ProductHelpLink">https://github.com/VSCodium/vscodium</String>
  <String Id="ProductUrlInfoAbout">https://github.com/VSCodium/vscodium</String>
  <String Id="ProductUrlUpdateInfo">https://github.com/VSCodium/vscodium</String>
  <String Id="PackageDescription">Paket mit @@PRODUCT_NAME@@</String>
  <String Id="FeatureStartMenuShortcutTitle">Startmenü-Verknüpfung</String>
  <String Id="FeatureStartMenuShortcutDescription">Startmenü-Symbol erstellen.</String>
  <String Id="FeatureDesktopShortcutTitle">Desktop-Verknüpfung</String>
  <String Id="FeatureDesktopShortcutDescription">Desktop-Symbol erstellen.</String>
  <String Id="FeatureFileTypeAssociationsTitle">Dateizuordnungen</String>
  <String Id="FeatureFileTypeAssociationsDescription">@@PRODUCT_NAME@@ als Editor für unterstützte Dateitypen registrieren.</String>
  <String Id="FeatureAddContextMenuFilesTitle">Dateikontextmenü</String>
  <String Id="FeatureAddContextMenuFilesDescription">Aktion "Mit @@PRODUCT_NAME@@ öffnen" dem Dateikontextmenü von Windows-Explorer hinzufügen.</String>
  <String Id="FeatureAddContextMenuFoldersTitle">Verzeichniskontextmenü</String>
  <String Id="FeatureAddContextMenuFoldersDescription">Aktion "Mit @@PRODUCT_NAME@@ öffnen" dem Verzeichniskontextmenü von Windows-Explorer hinzufügen.</String>
  <String Id="FeatureEnvironmentTitle">Zu PATH hinzufügen</String>
  <String Id="FeatureEnvironmentDescription">@@PRODUCT_NAME@@ zur PATH Variable hinzufügen. Nach dem Neustart verfügbar.</String>
  <String Id="LaunchApplication">@@PRODUCT_NAME@@ ausführen</String>
  <String Id="NewerVersionInstalled">Eine neuere Version von @@PRODUCT_NAME@@ ist bereits installiert.</String>
  <String Id="MinimumNetFramworkRequired">Diese Anwendung erfordert .NET Framework 4.5.2 oder höher. Bitte installieren Sie .NET Framework und führen Sie dieses Installationsprogramm erneut aus.</String>
  <String Id="OSVersionRequired">Windows 7 oder neuer ist erforderlich.</String>
</WixLocalization>
