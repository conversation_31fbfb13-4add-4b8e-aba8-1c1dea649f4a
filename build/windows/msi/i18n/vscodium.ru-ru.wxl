<?xml version="1.0" encoding="utf-8"?>
<WixLocalization Culture="ru-ru" Codepage="1251" xmlns="http://schemas.microsoft.com/wix/2006/localization">
  <String Id="ProductLanguage">1049</String>
  <String Id="ProductName">@@PRODUCT_NAME@@</String>
  <String Id="ProductHelpLink">https://github.com/VSCodium/vscodium</String>
  <String Id="ProductUrlInfoAbout">https://github.com/VSCodium/vscodium</String>
  <String Id="ProductUrlUpdateInfo">https://github.com/VSCodium/vscodium</String>
  <String Id="PackageDescription">Пакет с @@PRODUCT_NAME@@</String>
  <String Id="FeatureStartMenuShortcutTitle">Ярлык в меню «Пуск»</String>
  <String Id="FeatureStartMenuShortcutDescription">Создайте значок в меню «Пуск».</String>
  <String Id="FeatureDesktopShortcutTitle">Ярлык рабочего стола</String>
  <String Id="FeatureDesktopShortcutDescription">Создать значок на рабочем столе.</String>
  <String Id="FeatureFileTypeAssociationsTitle">Ассоциации файлов</String>
  <String Id="FeatureFileTypeAssociationsDescription">Зарегистрировать @@PRODUCT_NAME@@ в качестве редактора поддерживаемых типов файлов.</String>
  <String Id="FeatureAddContextMenuFilesTitle">Контекстное меню файлов</String>
  <String Id="FeatureAddContextMenuFilesDescription">Добавить действие «Открыть с помощью @@PRODUCT_NAME@@» в контекстное меню файлов Проводника Windows.</String>
  <String Id="FeatureAddContextMenuFoldersTitle">Контекстное меню каталога</String>
  <String Id="FeatureAddContextMenuFoldersDescription">Добавить действие «Открыть с помощью @@PRODUCT_NAME@@» в контекстное меню каталога Проводника Windows.</String>
  <String Id="FeatureEnvironmentTitle">Добавить в PATH</String>
  <String Id="FeatureEnvironmentDescription">Добавить @@PRODUCT_NAME@@ в переменную среды PATH. Доступно после перезапуска.</String>
  <String Id="LaunchApplication">Запустить @@PRODUCT_NAME@@</String>
  <String Id="NewerVersionInstalled">Новая версия @@PRODUCT_NAME@@ уже установлена.</String>
  <String Id="MinimumNetFramworkRequired">Приложению требуется .NET Framework 4.5.2 или более новая версии. Пожалуйста, установите .NET Framework, затем снова запустите установщик.</String>
  <String Id="OSVersionRequired">Требуется Windows 7 или более новая версия.</String>
</WixLocalization>
