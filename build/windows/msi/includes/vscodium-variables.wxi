﻿<?xml version="1.0" encoding="utf-8"?>
<Include>
  <!-- Setup UI strings. -->
  <?if $(env.Platform)=x86?>
    <?define ProgramFilesFolder="ProgramFilesFolder" ?>
    <?define Win64="no" ?>
    <?define Arch="(x86)" ?>
  <?else?>
    <?define ProgramFilesFolder="ProgramFiles64Folder" ?>
    <?define Win64="yes" ?>
    <?define Arch="(x64)" ?>
  <?endif?>

  <?define ProductName="!(loc.ProductName)" ?>
  <?define ProductNameWithVersion="!(loc.ProductName) $(var.ProductVersion) $(var.Arch)" ?>
  <?define ProductLanguage="!(loc.ProductLanguage)" ?>
  <?define AppFolderName="$(var.AppName)" ?>
  <?define ProductManufacturerShort="$(var.ManufacturerName)" ?>
  <?define ProductManufacturerLong="$(var.ManufacturerName)" ?>

  <!-- Static settings, DO NOT TOUCH or upgrades will break! -->
  <?define ProductUpgradeCode="{@@PRODUCT_UPGRADE_CODE@@}" ?>
  <?define RTMProductVersion="0.0.1" ?>
</Include>
