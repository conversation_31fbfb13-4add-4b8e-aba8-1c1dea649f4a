# Based on
# https://github.com/AppImage/pkg2appimage/blob/master/recipes/VSCode.yml
#
# On a Debian/Ubuntu system:
# wget -c "https://github.com/AppImage/pkg2appimage/raw/master/pkg2appimage"
# bash -ex pkg2appimage VSCodium

app: @@NAME@@

ingredients:
  packages:
    - code
    - libgconf2-4
  dist: trusty
  sources:
    - deb http://archive.ubuntu.com/ubuntu/ trusty main universe
  script:
    - pwd
    - cp ../../../../vscode/.build/linux/deb/amd64/deb/*.deb .
    - ls @@APPNAME@@_*.deb | cut -d _ -f 2 > VERSION

script:
  - sed -i -e 's|/usr/share/pixmaps/||g' usr/share/applications/@@APPNAME@@.desktop
  - cp usr/share/applications/@@APPNAME@@.desktop .
  - cp usr/share/pixmaps/@@ICON@@.png .
  - /usr/bin/convert @@ICON@@.png -resize 512x512 usr/share/icons/hicolor/512x512/apps/@@ICON@@.png
  - /usr/bin/convert @@ICON@@.png -resize 256x256 usr/share/icons/hicolor/256x256/apps/@@ICON@@.png
  - /usr/bin/convert @@ICON@@.png -resize 128x128 usr/share/icons/hicolor/128x128/apps/@@ICON@@.png
  - /usr/bin/convert @@ICON@@.png -resize 64x64 usr/share/icons/hicolor/64x64/apps/@@ICON@@.png
  - /usr/bin/convert @@ICON@@.png -resize 48x48 usr/share/icons/hicolor/48x48/apps/@@ICON@@.png
  - /usr/bin/convert @@ICON@@.png -resize 32x32 usr/share/icons/hicolor/32x32/apps/@@ICON@@.png
  - ( cd usr/bin/ ; ln -s ../share/@@APPNAME@@/@@APPNAME@@  . )
  - rm -rf usr/lib/x86_64-linux-gnu
  - rm -f lib/x86_64-linux-gnu/libglib*
  - cat > AppRun <<\EOF
  - #!/bin/sh
  - HERE="$(dirname "$(readlink -f "${0}")")"
  - export PATH="${HERE}"/usr/bin/:"${HERE}"/usr/sbin/:"${HERE}"/usr/games/:"${HERE}"/bin/:"${HERE}"/sbin/:"${PATH}"
  - export LD_LIBRARY_PATH="${HERE}"/usr/lib/:"${HERE}"/usr/lib32/:"${HERE}"/usr/lib64/:"${HERE}"/lib/:"${HERE}"/lib/i386-linux-gnu/:"${HERE}"/lib/x86_64-linux-gnu/:"${HERE}"/lib32/:"${HERE}"/lib64/:"${LD_LIBRARY_PATH}"
  - export XDG_DATA_DIRS="${HERE}"/usr/share/:"${XDG_DATA_DIRS}"
  - export PERLLIB="${HERE}"/usr/share/perl5/:"${HERE}"/usr/lib/perl5/:"${PERLLIB}"
  - export GSETTINGS_SCHEMA_DIR="${HERE}"/usr/share/glib-2.0/schemas/:"${GSETTINGS_SCHEMA_DIR}"
  - export QT_PLUGIN_PATH="${HERE}"/usr/lib/qt4/plugins/:"${HERE}"/usr/lib/i386-linux-gnu/qt4/plugins/:"${HERE}"/usr/lib/x86_64-linux-gnu/qt4/plugins/:"${HERE}"/usr/lib32/qt4/plugins/:"${HERE}"/usr/lib64/qt4/plugins/:"${HERE}"/usr/lib/qt5/plugins/:"${HERE}"/usr/lib/i386-linux-gnu/qt5/plugins/:"${HERE}"/usr/lib/x86_64-linux-gnu/qt5/plugins/:"${HERE}"/usr/lib32/qt5/plugins/:"${HERE}"/usr/lib64/qt5/plugins/:"${QT_PLUGIN_PATH}"
  - EXEC=$(grep -e '^Exec=.*' "${HERE}"/*.desktop | head -n 1 | cut -d "=" -f 2- | sed -e 's|%.||g')
  - exec ${EXEC} "$@"
  - EOF
