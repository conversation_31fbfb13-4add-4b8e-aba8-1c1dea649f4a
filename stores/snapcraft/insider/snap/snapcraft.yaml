name: codium-insiders
adopt-info: codium-insiders
summary: Code editing. Redefined.
description: |
  Binary releases of Visual Studio Code - Insiders without branding/telemetry/licensing

base: core20
grade: stable
confinement: classic
compression: lzo

parts:
  codium-insiders:
    plugin: nil
    override-build: |
      set -eu
      # Get .deb url
      wget --quiet https://api.github.com/repos/VSCodium/vscodium-insiders/releases -O latest.json
      VERSION=$( jq -r 'sort_by(.tag_name|split("-")[0]|split(".")|map(tonumber))|last.tag_name' latest.json )
      DEB_URL=$( jq -r 'map(select(.tag_name == "'"${VERSION}"'"))|first.assets[].browser_download_url|select(endswith("'"_${SNAPCRAFT_TARGET_ARCH}.deb"'"))' latest.json )
      DEB_NAME=$( basename "${DEB_URL}" )
      # Downloading .deb
      wget "${DEB_URL}" -O "${SNAPCRAFT_PART_INSTALL}/${DEB_NAME}"
      # Unpacking .deb
      dpkg -x "${SNAPCRAFT_PART_INSTALL}/${DEB_NAME}" "${SNAPCRAFT_PART_INSTALL}"
      # Clean up
      rm -f latest.json
      rm -f "${SNAPCRAFT_PART_INSTALL}/${DEB_NAME}"
      # Set version
      snapcraftctl set-version "${VERSION/-*/}"
      # Prepare GUI
      mkdir -p "${SNAPCRAFT_PART_INSTALL}/meta/gui"
      cp "${SNAPCRAFT_PART_INSTALL}/usr/share/codium-insiders/resources/app/resources/linux/code.png" "${SNAPCRAFT_PART_INSTALL}/meta/gui/codium-insiders.png"
      # Update paths
      sed -i 's|Exec=/usr/share/codium/codium-insiders|Exec=codium-insiders --force-user-env|g' "${SNAPCRAFT_PART_INSTALL}/usr/share/applications/codium-insiders.desktop"
      sed -i 's|Exec=/usr/share/codium/codium-insiders|Exec=codium-insiders --force-user-env|g' "${SNAPCRAFT_PART_INSTALL}/usr/share/applications/codium-insiders-url-handler.desktop"
      sed -i 's|Icon=vscodium-insiders|Icon=${SNAP}/meta/gui/codium-insiders.png|g' "${SNAPCRAFT_PART_INSTALL}/usr/share/applications/codium-insiders.desktop"
      sed -i 's|Icon=vscodium-insiders|Icon=${SNAP}/meta/gui/codium-insiders.png|g' "${SNAPCRAFT_PART_INSTALL}/usr/share/applications/codium-insiders-url-handler.desktop"
    build-packages:
      - wget
      - jq
    stage-packages:
      - ca-certificates
      - libasound2
      - libatk-bridge2.0-0
      - libatk1.0-0
      - libatspi2.0-0
      - libcairo2
      - libcanberra-gtk3-module
      - libcurl3-gnutls
      - libcurl3-nss
      - libcurl4
      - libdrm2
      - libgbm1
      - libgl1
      - libglib2.0-0
      - libgtk-3-0
      - libibus-1.0-5
      - libnss3
      - libpango-1.0-0
      - libsecret-1-0
      - libvulkan1
      - libwayland-egl1
      - libxcomposite1
      - libxdamage1
      - libxfixes3
      - libxkbcommon0
      - libxkbfile1
      - libxrandr2
      - libxss1
      - locales-all
      - packagekit-gtk3-module
      - gnome-settings-daemon-common
      - xdg-utils
    prime:
      - -lib/udev
      - -usr/lib/systemd
      - -usr/share/doc
      - -usr/share/fonts
      - -usr/share/icons
      - -usr/share/lintian
      - -usr/share/man
      - -usr/share/codium/chrome-sandbox
      - -usr/share/@@NAME@@/libEGL.so*
      - -usr/share/@@NAME@@/libGLESv2.so*
      - -usr/share/@@NAME@@/libvulkan.so*
    build-attributes:
      - enable-patchelf
    override-prime: |
      set -eux
      snapcraftctl prime
      for snap in "core20"; do
        cd "/snap/${snap}/current/usr/lib/${SNAPCRAFT_ARCH_TRIPLET}"
        find . -type f,l -name "*.so*" -exec bash -c "rm -f ${SNAPCRAFT_PRIME}/{}*" \;
      done

  electron-launch:
    after:
      - codium-insiders
    plugin: dump
    source: snap/local/bin

apps:
  codium:
    command: electron-launch $SNAP/usr/share/codium-insiders/bin/codium-insiders --no-sandbox
    desktop: usr/share/applications/codium-insiders.desktop
    common-id: codium-insiders.desktop

  url-handler:
    command: electron-launch $SNAP/usr/share/codium-insiders/bin/codium-insiders --open-url --no-sandbox
    desktop: usr/share/applications/codium-insiders-url-handler.desktop
